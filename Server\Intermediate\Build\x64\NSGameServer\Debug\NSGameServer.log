﻿  컴파일러 패스:
   C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\CL.exe:        버전 19.44.35211.0
   C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\c1.dll:        버전 19.44.35211.0
   C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\c1xx.dll:      버전 19.44.35211.0
   C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\c2.dll:        버전 19.44.35211.0
   C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\c1xx.dll:      버전 19.44.35211.0
   C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe:      버전 14.44.35211.0
   C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\mspdb140.dll:  버전 14.44.35211.0
   C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\1042\clui.dll: 버전 19.44.35211.0
  NSMonsterActionNodeCustom.cpp
  NSMonsterActionNodeNoPath.cpp
  NSMonsterActionNodeSelector.cpp
  NSMonsterActionNodeSequence.cpp
  NSMonsterActionNodeSkill.cpp
  NSSearchManager.cpp
  NSBTTaskLevelSequence.cpp
  NSBTTaskMoveToLevelSequenceStartPos.cpp
  NSBTTaskPathFindRotate.cpp
  NSBTTaskReturn.cpp
  NSBTTaskRotate.cpp
  NSClassPointComponent.cpp
  NSCrowdControlComponent.cpp
  NSDuelComponent.cpp
  NSExecComponent.cpp
  NSGuardComponent.cpp
  NSInteractionComponent.cpp
  NSMonsterActionComponent.cpp
  NSMonsterSoulComponent.cpp
  NSMovementComponent.cpp
  NSRelationComponent.cpp
  NSSkillComponent.cpp
  NSStatComponent.cpp
  NSStateComponent.cpp
  NSStatusEffectComponent.cpp
  NSSummonerComponent.cpp
  NSDamageMaterial_BacklashDebuff.cpp
  NSDamageMaterial_DamageChangeFactor.cpp
  NSPostDamageCalculator.cpp
  NSDamageApplier.cpp
  NSHitStateCalculator.cpp
  NSDuelManager.cpp
  NSGameAreaOfEffect.cpp
  NSGameHero.cpp
  NSGameMonster.cpp
  NSGameSummon.cpp
  NSCombatEventFacade.cpp
  GmCmdHero.cpp
  GmCmdStatusEffect.cpp
  NSCell.cpp
  NSMapSystem.cpp
  NSPartyMember.cpp
  NSPassiveCondition_AddStatusEffect.cpp
  NSPassiveCondition_GiveDamage.cpp
  NSPassiveCondition_HaveStatusEffect.cpp
  NSPassiveCondition_Kill.cpp
  NSPassiveCondition_TargetStatusEffectCount.cpp
  NSPassiveEffect_AddStatusEffect.cpp
  NSQuestStep.cpp
  NSAddStatusEffectByStatusEffectExecProperty.cpp
  NSAddStatusEffectExecProperty.cpp
  NSBacklashExecProperty.cpp
  NSDamageByStatusEffectStackExecProperty.cpp
  NSExtraDamageExecProperty.cpp
  NSFutureTimeChangeExecProperty.cpp
  NSGainClassPointByStatusEffectExecProperty.cpp
  NSHealByHPWithBuffStackExecProperty.cpp
  NSRemoveCrowdControlExecProperty.cpp
  NSRemoveStatusEffectExecProperty.cpp
  NSRemoveStatusEffectStackExecProperty.cpp
  NSRemoveTimeAccelerationExecProperty.cpp
  NSStatusEffectExtensionExecProperty.cpp
  NSStealChronoRemnantsExecProperty.cpp
  NSSummonAOEExecProperty.cpp
  NSSummonMonsterSoulExecProperty.cpp
  NSTimeRewindExecProperty.cpp
  NSToggleStatusEffectExecProperty.cpp
  NSExecProperty.cpp
  NSSkillConstraintsChecker.cpp
  NSSkillContext.cpp
  NSSkillCooler.cpp
  NSStatusEffectEndCondition_ActiveSkillEventEnd.cpp
  NSStatusEffectEndCondition_Attack.cpp
  NSStatusEffectEndCondition_BattleModeOff.cpp
  NSStatusEffectEndCondition_BattleModeOn.cpp
  NSStatusEffectEndCondition_CPRateMax.cpp
  NSStatusEffectEndCondition_CPRateMin.cpp
  NSStatusEffectEndCondition_CrowdControl.cpp
  NSStatusEffectEndCondition_DamageSkillEnd.cpp
  NSStatusEffectEndCondition_Damaged.cpp
  NSStatusEffectEndCondition_EndLevelSequence.cpp
  NSStatusEffectEndCondition_HPRateMax.cpp
  NSStatusEffectEndCondition_HPRateMin.cpp
  NSStatusEffectEndCondition_Interaction.cpp
  NSStatusEffectEndCondition_MoveStateOff.cpp
  NSStatusEffectEndCondition_MoveStateOn.cpp
  NSStatusEffectEndCondition_NextActiveSkillEnd.cpp
  NSStatusEffectEndCondition_NextActiveSkillEventEnd.cpp
  NSStatusEffectEndCondition_NextSkillEnd.cpp
  NSStatusEffectEndCondition_NextSkillEventEnd.cpp
  NSStatusEffectEndCondition_PickUpEnd.cpp
  NSStatusEffectEndCondition_RegionId.cpp
  NSStatusEffectEndCondition_RelationChange.cpp
  NSStatusEffectEndCondition_RemovePassiveGroupId.cpp
  NSStatusEffectEndCondition_RemovePassiveId.cpp
  NSStatusEffectEndCondition_SkillEnd.cpp
  NSStatusEffectEndCondition_SkillEventEnd.cpp
  NSStatusEffectEndCondition_SprintEnd.cpp
  NSStatusEffectEndCondition_StaminaRateMax.cpp
  NSStatusEffectEndCondition_StaminaRateMin.cpp
  NSStatusEffectEndCondition_StatusEffectGroupId.cpp
  NSStatusEffectEndCondition_StatusEffectGroupIdEnd.cpp
  NSStatusEffectEndCondition_StatusEffectId.cpp
  NSStatusEffectEndCondition_StatusEffectIdEnd.cpp
  NSStatusEffectEndCondition_Time.cpp
  NSStatusEffectEndCondition_TriggerVolumeId.cpp
  NSStatusEffectEndCondition_UseActionBlockSkill.cpp
  NSStatusEffectEndCondition_UseMoveBlockSkill.cpp
  NSStatusEffectEndCondition_UseSkill.cpp
  NSStatusEffectEndCondition_UseSkillFamilyId.cpp
  NSStatusEffectEndCondition_UseSkillId.cpp
  NSStatusEffectEndCondition_WeaponSwap.cpp
  NSStatusEffectEndConditionFactory.cpp
  NSStatusEffectFunction_AddTimeRewindPoint.cpp
  NSStatusEffectFunction_BoundActor.cpp
  NSStatusEffectFunction_CPBlockChange.cpp
  NSStatusEffectFunction_ChangeRelation.cpp
  NSStatusEffectFunction_ChronotectorTimeAcceleration.cpp
  NSStatusEffectFunction_DamageByHit.cpp
  NSStatusEffectFunction_DamageShield.cpp
  NSStatusEffectFunction_Dot1.cpp
  NSStatusEffectFunction_Hot1OnMaxLifeNoDisp.cpp
  NSStatusEffectFunction_Hot1OnMaximumLife.cpp
  NSStatusEffectFunction_HyperArmor.cpp
  NSStatusEffectFunction_Immortal.cpp
  NSStatusEffectFunction_IncreaseStatByStack.cpp
  NSStatusEffectFunction_IncreaseStatRateByStack.cpp
  NSStatusEffectFunction_Lightning.cpp
  NSStatusEffectFunction_OnExpireSkillCooldown.cpp
  NSStatusEffectFunction_OnRankExec.cpp
  NSStatusEffectFunction_OnStackExec.cpp
  NSStatusEffectFunction_ShieldByBuffCount.cpp
  NSStatusEffectFunction_SkillDamage.cpp
  NSStatusEffectFunction_SkillLock.cpp
  NSStatusEffectFunction_SpecialStateDataID.cpp
  NSStatusEffectFunction_StatusEffectImmune.cpp
  NSStatusEffectFunction_Thorn.cpp
  NSStatusEffectFunction_TickEvent.cpp
  NSStatusEffectFunction_TickEventForToggle.cpp
  NSStatusEffectFunction_TickEventPer3.cpp
  NSStatusEffect.cpp
  NSCampSystem.cpp
  NSCharacterPartsSystem.cpp
  NSCharacterSystem.cpp
  NSInteractionSystem.cpp
  NSSkillSystem.cpp
  NSStatusEffectProviderSystem.cpp
  NSAggroUtil.cpp
  NSExecPropertyUtil.cpp
  NSStatusEffectUtil.cpp
  NSUnitUtil.cpp
  NSVehicleEffect_AddStatusEffect.cpp
  NSVehicleEffect_Exec.cpp
  NSGameServer.vcxproj -> C:\work\Chrono\ProjectSServer\Server\Binaries\x64\NSGameServer-x64-Debug.exe
