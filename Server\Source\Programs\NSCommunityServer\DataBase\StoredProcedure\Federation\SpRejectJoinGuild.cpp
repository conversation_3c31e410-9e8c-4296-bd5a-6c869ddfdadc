﻿#include "stdafx.h"
#include "SpRejectJoinGuild.h"

#include "ADO/NSAdoCommand.h"

SpRejectJoinGuild::SpRejectJoinGuild(const int64_t GuildId, const int64_t Cid, const int64_t WaitingCid, const int32_t ValidDay) :
	Input{ GuildId, Cid, WaitingCid, ValidDay }
{
}

EErrorCode SpRejectJoinGuild::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("CID", Input.Cid);
	command->SetItem("WaitingCID", Input.WaitingCid);
	command->SetItem("ValidDay", Input.ValidDay);

	return EErrorCode::None;
}

//EErrorCode SpRejectJoinGuild::MakeOutput(NSAdoCommand* command)
//{
//	command->GetItem("@InviteUid", Output.InviteUid);
//
//	return EErrorCode::None;
//}

EErrorCode SpRejectJoinGuild::HandleReturnValue(const int32_t returnValue)
{
	switch (returnValue)
	{
	case EProcReturnValue::GuildMemberWaitingInvalid:
		return EErrorCode::GuildMemberWaitingInvalid;
	default:
		return EErrorCode::DBError;
	}
}
