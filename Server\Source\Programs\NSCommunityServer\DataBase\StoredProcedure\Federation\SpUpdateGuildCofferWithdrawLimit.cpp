﻿#include "stdafx.h"
#include "SpUpdateGuildCofferWithdrawLimit.h"

#include "ADO/NSAdoCommand.h"

SpUpdateGuildCofferWithdrawLimit::SpUpdateGuildCofferWithdrawLimit(const int64_t GuildId, const int64_t Cid, const TYPE_QUANTITY WithdrawLimit) :
	Input{ GuildId, Cid, WithdrawLimit }
{
}

EErrorCode SpUpdateGuildCofferWithdrawLimit::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("CID", Input.Cid);
	command->SetItem("WithdrawLimit", Input.WithdrawLimit);

	return EErrorCode::None;
}

//EErrorCode SpUpdateGuildCofferWithdrawLimit::MakeOutput(NSAdoCommand* command)
//{
//	command->GetItem("@ResultHistory", Output.ResultHistory, 4000);
//
//	return EErrorCode::None;
//}

//EErrorCode SpUpdateGuildCofferWithdrawLimit::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}
