#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpSetGuildCofferBalance : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spGmCmdSetGuildCofferBalance";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	// EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int64_t GuildId = 0;
		TYPE_QUANTITY Amount = 0;
	} Input;

	struct Data
	{
		int32_t CallbackId = 0;
	} Data;

	SpSetGuildCofferBalance() = default;
	SpSetGuildCofferBalance(const int64_t GuildId, const TYPE_QUANTITY Amount);
};
