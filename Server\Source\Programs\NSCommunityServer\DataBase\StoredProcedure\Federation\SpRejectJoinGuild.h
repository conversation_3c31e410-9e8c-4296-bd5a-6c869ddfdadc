﻿#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpRejectJoinGuild : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spRejectJoinGuild";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	//EErrorCode MakeOutput(NSAdoCommand* command);


public:
	EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int64_t GuildId = 0;
		int64_t Cid = 0;
		int64_t WaitingCid = 0;
		int32_t ValidDay = 0;
	} Input;

	SpRejectJoinGuild() = default;
	SpRejectJoinGuild(const int64_t GuildId, const int64_t Cid, const int64_t WaitingCid, const int32_t ValidDay);
};
