#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"
#include "CommunityServer/NSGuild.h"

class SpSearchGuild : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spSearchGuild";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	//EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int32_t Wid = 0;
		int32_t Count = 0;
		char Name[g_uMaxGuildNameUTF8Length] = {};
	} Input;

	SpSearchGuild() = default;
	SpSearchGuild(const int32_t Wid, const int32_t Count, const char* Name);
};
