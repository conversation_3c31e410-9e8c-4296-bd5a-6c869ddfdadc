#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpGetRecommendGuild : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spGetRecommendGuild";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	//EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int32_t Wid = 0;
		int32_t AcceptLevel = 0;
		int32_t AcceptGearScore = 0;
		int32_t Count = 0;
	} Input;

	SpGetRecommendGuild() = default;
	SpGetRecommendGuild(const int32_t Wid, const int32_t AcceptLevel, const int32_t AcceptGearScore, const int32_t Count);
};
