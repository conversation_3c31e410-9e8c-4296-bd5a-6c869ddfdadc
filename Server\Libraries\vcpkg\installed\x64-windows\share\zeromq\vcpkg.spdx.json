{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/zeromq-x64-windows-4.3.5#2-61a80a02-bf24-4a10-bfaf-7de870d9a308", "name": "zeromq:x64-windows@4.3.5#2 baaa3b5af46fd88b63e6317df954435730d436c46e35df71085a5a00a23bf59f", "creationInfo": {"creators": ["Tool: vcpkg-2025-01-29-a75ad067f470c19f030361064e32a2585392bee2"], "created": "2025-06-17T12:06:18Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "zeromq", "SPDXID": "SPDXRef-port", "versionInfo": "4.3.5#2", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/zeromq", "homepage": "https://github.com/zeromq/libzmq", "licenseConcluded": "MPL-2.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "The ZeroMQ lightweight messaging kernel is a library which extends the standard socket interfaces with features traditionally provided by specialised messaging middleware products", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "zeromq:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "baaa3b5af46fd88b63e6317df954435730d436c46e35df71085a5a00a23bf59f", "downloadLocation": "NONE", "licenseConcluded": "MPL-2.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "zeromq/libzmq", "downloadLocation": "git+https://github.com/zeromq/libzmq@v4.3.5", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "108d9c5fa761c111585c30f9c651ed92942dda0ac661155bca52cc7b6dbeb3d27b0dd994abde206eacfc3bc88d19ed24e45b291050c38469e34dca5f8c9a037d"}]}], "files": [{"fileName": "./F:/Work/ProjectS/ProjectSServer/Server/Libraries/vcpkg/ports/zeromq/fix-arm.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "4d231802a3981e3beb343818314826e58813732bfd70851c31b76682b88f0b97"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./F:/Work/ProjectS/ProjectSServer/Server/Libraries/vcpkg/ports/zeromq/portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "f6df781bc3958f71bdbc99bbca3427ff939b934c7272fd66f859f6f6255e2e88"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./F:/Work/ProjectS/ProjectSServer/Server/Libraries/vcpkg/ports/zeromq/vcpkg-cmake-wrapper.cmake", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "19043ac8e3a04cce2d94c59de9909704fa130cf2a340ea164d2a484bc7ce66dc"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./F:/Work/ProjectS/ProjectSServer/Server/Libraries/vcpkg/ports/zeromq/vcpkg.json", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "7f4a690dc969072846cb7573a453b44597672351307cdd1c81216cac7aa8395f"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}