﻿#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpUpdateGuildCofferWithdrawLimit : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spUpdateGuildCofferWithdrawLimit";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	//EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int64_t GuildId = 0;
		int64_t Cid = 0;
		TYPE_QUANTITY WithdrawLimit = 0;
	} Input;

	SpUpdateGuildCofferWithdrawLimit() = default;
	SpUpdateGuildCofferWithdrawLimit(const int64_t GuildId, const int64_t Cid, const TYPE_QUANTITY WithdrawLimit);
};
