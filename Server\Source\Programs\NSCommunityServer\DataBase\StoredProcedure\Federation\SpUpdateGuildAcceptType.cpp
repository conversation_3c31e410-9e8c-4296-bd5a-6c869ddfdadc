﻿#include "stdafx.h"
#include "SpUpdateGuildAcceptType.h"

#include "ADO/NSAdoCommand.h"

SpUpdateGuildAcceptType::SpUpdateGuildAcceptType(const int64_t GuildId, const int64_t Cid, const uint8_t AcceptType) :
	Input{ GuildId, Cid, AcceptType }
{
}

EErrorCode SpUpdateGuildAcceptType::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("CID", Input.Cid);
	command->SetItem("AcceptType", Input.AcceptType);

	return EErrorCode::None;
}

//EErrorCode SpUpdateGuildAcceptType::MakeOutput(NSAdoCommand* command)
//{
//	command->GetItem("@InviteUid", Output.InviteUid);
//
//	return EErrorCode::None;
//}

EErrorCode SpUpdateGuildAcceptType::HandleReturnValue(const int32_t returnValue)
{
	switch (returnValue)
	{
	case EProcReturnValue::GuildMemberWaitingNotEmpty:
		return EErrorCode::GuildMemberWaitingNotEmpty;
	default:
		return EErrorCode::DBError;
	}
}
