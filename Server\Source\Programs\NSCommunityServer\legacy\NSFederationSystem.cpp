﻿#include "stdafx.h"
#include "NSFederationSystem.h"

#include "Module/NSCommunityModule.h"

//#include "Connector/CommunityServer/NSCommunityServerConnector.h"
//#include "Network/Session/NSSessionManager.h"
//#include "Guild/NSGuildManager.h"
#include "Network/Session/NSSession.h"
#include "CommunityServer/NSGuild.h"

#include "Data/NSGameConfigTemplate.h"
#include "Data/NSContentCooldownTemplate.h"
#include "Data/NSGuildTemplate.h"

#include "NSTickManager/NSTickManager.h"

#include "DataBase/StoredProcedure/NSStoredProcedureBatch.h"
#include "DataBase/StoredProcedure/Federation/SpCheckGuildName.h"
#include "DataBase/StoredProcedure/Federation/SpSearchGuild.h"
#include "DataBase/StoredProcedure/Federation/SpGetRecommendGuild.h"
#include "DataBase/StoredProcedure/Federation/SpCreateGuild.h"
#include "DataBase/StoredProcedure/Federation/SpDismissGuild.h"
#include "DataBase/StoredProcedure/Federation/SpUpdateGuildEmblem.h"
#include "DataBase/StoredProcedure/Federation/SpUpdateGuildProfile.h"
#include "DataBase/StoredProcedure/Federation/SpUpdateGuildAcceptType.h"
#include "DataBase/StoredProcedure/Federation/SpUpdateGuildAcceptLimit.h"
#include "DataBase/StoredProcedure/Federation/SpUpdateGuildNotice.h"
#include "DataBase/StoredProcedure/Federation/SpGetGuildInfo.h"
#include "DataBase/StoredProcedure/Federation/SpGetGuildHistory.h"
#include "DataBase/StoredProcedure/Federation/SpGetGuildMember.h"
#include "DataBase/StoredProcedure/Federation/SpGetGuildWaiting.h"
#include "DataBase/StoredProcedure/Federation/SpJoinGuild.h"
#include "DataBase/StoredProcedure/Federation/SpCancelJoinGuild.h"
#include "DataBase/StoredProcedure/Federation/SpGetGuildJoinWaiting.h"
#include "DataBase/StoredProcedure/Federation/SpApproveJoinGuild.h"
#include "DataBase/StoredProcedure/Federation/SpRejectJoinGuild.h"
#include "DataBase/StoredProcedure/Federation/SpInviteGuild.h"
#include "DataBase/StoredProcedure/Federation/SpAcceptGuild.h"
#include "DataBase/StoredProcedure/Federation/SpDeclineGuild.h"
#include "DataBase/StoredProcedure/Federation/SpLeaveGuild.h"
#include "DataBase/StoredProcedure/Federation/SpKickGuildMember.h"
#include "DataBase/StoredProcedure/Federation/SpUpdateGuildMemberGrade.h"
#include "DataBase/StoredProcedure/Federation/SpTransferGuildMaster.h"
#include "DataBase/StoredProcedure/Federation/SpUpdateGuildMemberComment.h"
#include "DataBase/StoredProcedure/Federation/SpGetNewGuildMasterByAfk.h"
#include "DataBase/StoredProcedure/Federation/SpSearchGuildInfo.h"

NSFederationSystem::NSFederationSystem(NSCommunityModule* communityModule)
	: NSSystemBase(communityModule)
{
}

bool NSFederationSystem::Init()
{
	INSERT_PROCESSOR(NSFederationSystem::PacketSearchGuildInfoReq, NSPacketSearchGuildInfoReq);

	return true;
}

bool NSFederationSystem::Reset()
{
	return true;
}

void NSFederationSystem::PacketLoadGuildDataReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketLoadGuildDataReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto ownerCid = pcReq->GetOwnerCID();
// 	auto guildId = pcReq->GetGuildId();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild != nullptr)
// 	{
// 		if (!pGuild->IsGuildMember(ownerCid))
// 		{
// 			NSPacketLoadGuildDataNak cNak;
// 			cNak.SetSessionByAID(pcSession->GetAID());
// 			cNak.SetResult(EErrorCode::GuildMemberInvalidPermission);
// 			NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 			return;
// 		}
//
// 		SendGuildInfo(pcSession, pGuild->ToNPGuild(), true);
// 		SendGuildMember(pcSession, pGuild);
// 		SendGuildHistory(pcSession, pGuild);
// 		GuildMemberOnlineBroadcast(guildId, ownerCid, pcSession->GetAID());
// 		return;
// 	}
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		auto batch = dataSerializer.Create<NSStoredProcedureBatch>();
// 		batch->AddStoredProcedure(dataSerializer.Create<SpGetGuildInfo>(guildId, ownerCid));
// 		batch->AddStoredProcedure(dataSerializer.Create<SpGetGuildMember>(guildId));
// 		batch->AddStoredProcedure(dataSerializer.Create<SpGetGuildHistory>(guildId));
// 		batch->AddStoredProcedure(dataSerializer.Create<SpGetNewGuildMasterByAfk>(guildId));
// 		NSDataBaseManager::GetInstance()->PushQuery<NSStoredProcedureBatch>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultLoadGuildData));
// 	}
}

void NSFederationSystem::ResultLoadGuildData(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		NSPacketLoadGuildDataNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	//auto cData = pcQueryData->GetQueryData().Front<NSStoredProcedureBatch>();
// 	pcQueryData->GetQueryData().Next(); // TODO: [Austin]
//
// 	//auto cData = pcQueryData->GetQueryData().Front<SpGetGuildInfo>();
// 	auto cDataGuildInfo = pcQueryData->GetQueryData().Front<SpGetGuildInfo>();
// 	if (cDataGuildInfo == nullptr)
// 	{
// 		NSPacketLoadGuildDataNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
// 	auto guildId = cDataGuildInfo->Input.GuildId;
// 	auto ownerCid = cDataGuildInfo->Input.Cid;
// 	pcQueryData->GetQueryData().Next(); // TODO: [Austin]
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpGetGuildInfo::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		LOGW << fmt::format("Cannot find guild while loading. GuildID: {}", guildId);
// 		NSPacketLoadGuildDataNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotFound);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (recordSet->IsEOF())
// 	{
// 		LOGW << fmt::format("Cannot find guild while loading. GuildID: {}", guildId);
// 		NSPacketLoadGuildDataNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotFound);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto guildInfo = ProcessGuildInfo(recordSet);
// 	NSGuildManager::GetInstance()->AddGuild(guildInfo);
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildInfo.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketLoadGuildDataNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotFound);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	SendGuildInfo(pcSession, pGuild->ToNPGuild(), true);
// 	//auto cData = pcQueryData->GetQueryData().Front<SpGetGuildMember>();
// 	ResultGuildMemberList(pcQueryData);
// 	pcQueryData->GetQueryData().Next(); // TODO: [Austin]
//
// 	//auto cData = pcQueryData->GetQueryData().Front<SpGetGuildHistory>();
// 	ResultGuildHistory(pcQueryData);
// 	pcQueryData->GetQueryData().Next(); // TODO: [Austin]
//
// 	//auto cData = pcQueryData->GetQueryData().Front<SpGetNewGuildMasterByAfk>();
// 	ProcessNewGuildMasterByAfk(pcQueryData);
//
// 	GuildMemberOnlineBroadcast(guildInfo.GuildId, ownerCid, pcSession->GetAID());
}

void NSFederationSystem::PacketCheckGuildNameReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketCheckGuildNameReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto wid = pcReq->GetWID();
// 	auto name = pcReq->GetName();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpCheckGuildName>(wid, name);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpCheckGuildName>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultCheckGuildName));
// 	}
}

void NSFederationSystem::ResultCheckGuildName(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	//if (!pcQueryData->IsValid())
// 	//{
// 	//	LOGW << fmt::format("spCheckGuildName DB Error[{}]", pcQueryData->GetErrorCode());
//
// 	//	NSPacketCheckGuildNameNak cNak;
// 	//	cNak.SetSessionByAID(pcSession->GetAID());
// 	//	cNak.SetResult(EErrorCode::DBError);
// 	//	NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 	//	return;
// 	//}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpCheckGuildName>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketCheckGuildNameNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	NSPacketCheckGuildNameAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetResult(pcQueryData->GetErrorCode());
// 	cAck.SetName(cData->Input.Name);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketSearchGuildReq(NSPacket* pcPacket)
{
// 	static const auto maxCount = NSGameConfigTemplate::GetInstance()->GetGuildListMaxCount();
// 	auto pcReq = static_cast<NSPacketSearchGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto wid = pcReq->GetWID();
// 	auto name = pcReq->GetName();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpSearchGuild>(wid, maxCount, name);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpSearchGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultSearchGuild));
// 	}
}

void NSFederationSystem::ResultSearchGuild(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spSearchGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketSearchGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpSearchGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketSearchGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpSearchGuild::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		LOGD << fmt::format("spSearchGuild recordset is empty");
// 		return;
// 	}
//
// 	bool isEmpty = true;
// 	NSPacketSearchGuildAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	while (!recordSet->IsEOF())
// 	{
// 		isEmpty = false;
// 		NPGuild guildInfo;
// 		guildInfo.Wid = cData->Input.Wid;
// 		recordSet->GetItem("GuildID", guildInfo.GuildId);
// 		recordSet->GetItem("Level", guildInfo.Level);
// 		uint8_t acceptType = 0;
// 		recordSet->GetItem("MemberAccept", acceptType);
// 		guildInfo.AcceptType = from_ENpLib_GuildAcceptType(acceptType);
// 		recordSet->GetItem("AcceptLevel", guildInfo.AcceptLevel);
// 		recordSet->GetItem("AcceptGearScore", guildInfo.AcceptGearScore);
// 		recordSet->GetItem("CurrentMember", guildInfo.CurMember);
// 		recordSet->GetItem("MaxMember", guildInfo.MaxMember);
// 		recordSet->GetItem("Name", guildInfo.Name);
// 		recordSet->GetItem("MasterName", guildInfo.MasterName);
// 		recordSet->GetItem("Profile", guildInfo.Profile);
//
// 		recordSet->GetItem("Emblem", guildInfo.Emblem.Symbol);
// 		//recordSet->GetItem("EmblemColor", guildInfo.Emblem.SymbolColor);
// 		//recordSet->GetItem("EmblemBG", guildInfo.Emblem.Background);
// 		//recordSet->GetItem("EmblemBGColor", guildInfo.Emblem.BackgroundColor);
//
// 		if (!cAck.AddGuilds(guildInfo))
// 		{
// 			NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 			cAck.ClearGuilds();
// 			cAck.AddGuilds(guildInfo);
// 		}
// 	}
//
// 	if (isEmpty || !cAck.GetGuilds().empty())
// 	{
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 	}
}

void NSFederationSystem::PacketRecommendGuildReq(NSPacket* pcPacket)
{
// 	static const auto maxCount = NSGameConfigTemplate::GetInstance()->GetGuildListMaxCount();
// 	auto pcReq = static_cast<NSPacketRecommendGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto wid = pcReq->GetWID();
// 	auto acceptLevel = pcReq->GetAcceptLevel();
// 	auto acceptGearScore = pcReq->GetAcceptGearScore();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpGetRecommendGuild>(wid, acceptLevel, acceptGearScore, maxCount);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpGetRecommendGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultRecommendGuild));
// 	}
}

void NSFederationSystem::ResultRecommendGuild(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spGetRecommendGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketRecommendGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpGetRecommendGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketRecommendGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpGetRecommendGuild::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		LOGD << fmt::format("spGetRecommendGuild recordset is empty");
// 		return;
// 	}
//
// 	bool isEmpty = true;
// 	NSPacketRecommendGuildAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	while (!recordSet->IsEOF())
// 	{
// 		isEmpty = false;
// 		NPGuild guildInfo;
// 		guildInfo.Wid = cData->Input.Wid;
// 		recordSet->GetItem("GuildID", guildInfo.GuildId);
// 		recordSet->GetItem("Level", guildInfo.Level);
// 		uint8_t acceptType = 0;
// 		recordSet->GetItem("MemberAccept", acceptType);
// 		guildInfo.AcceptType = from_ENpLib_GuildAcceptType(acceptType);
// 		recordSet->GetItem("AcceptLevel", guildInfo.AcceptLevel);
// 		recordSet->GetItem("AcceptGearScore", guildInfo.AcceptGearScore);
// 		recordSet->GetItem("CurrentMember", guildInfo.CurMember);
// 		recordSet->GetItem("MaxMember", guildInfo.MaxMember);
// 		recordSet->GetItem("Name", guildInfo.Name);
// 		recordSet->GetItem("MasterName", guildInfo.MasterName);
// 		recordSet->GetItem("Profile", guildInfo.Profile);
//
// 		recordSet->GetItem("Emblem", guildInfo.Emblem.Symbol);
// 		//recordSet->GetItem("EmblemColor", guildInfo.Emblem.SymbolColor);
// 		//recordSet->GetItem("EmblemBG", guildInfo.Emblem.Background);
// 		//recordSet->GetItem("EmblemBGColor", guildInfo.Emblem.BackgroundColor);
//
// 		if (!cAck.AddGuilds(guildInfo))
// 		{
// 			NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 			cAck.ClearGuilds();
// 			cAck.AddGuilds(guildInfo);
// 		}
// 	}
//
// 	if (isEmpty || !cAck.GetGuilds().empty())
// 	{
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 	}
}

void NSFederationSystem::PacketCreateGuildReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketCreateGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto wid = pcReq->GetWID();
// 	auto aid = pcReq->GetAID();
// 	auto cid = pcReq->GetCID();
// 	auto newGuild = pcReq->GetNewGuild();
// 	auto consumePayload = pcReq->GetConsumePayload();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpCreateGuild>(wid, aid, cid, newGuild, consumePayload);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpCreateGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultCreateGuild));
// 	}
}

void NSFederationSystem::ResultCreateGuild(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spCreateGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketCreateGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpCreateGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketCreateGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet();
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketCreateGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (recordSet->IsEOF())
// 	{
// 		NSPacketCreateGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto guildInfo = ProcessGuildInfo(recordSet);
//
// 	NSPacketCreateGuildAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetNewGuild(guildInfo);
// 	cAck.SetResultConsume(cData->Output.ResultConsume);
// 	cAck.SetResultTimestamp(cData->Output.ResultTimestamp);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketDismissGuildReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketDismissGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpDismissGuild>(guildId, cid);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpDismissGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultDismissGuild));
// 	}
}

void NSFederationSystem::ResultDismissGuild(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spDismissGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketDismissGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpDismissGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketDismissGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	NSGuildManager::GetInstance()->RemoveGuild(cData->Input.GuildId);
//
// 	NSPacketDismissGuildAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetGuildTimestamp(cData->Output.GuildTimestamp);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketSetGuildEmblemReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketSetGuildEmblemReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
// 	auto emblem = pcReq->GetEmblem();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpUpdateGuildEmblem>(guildId, cid, emblem);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpUpdateGuildEmblem>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultSetGuildEmblem));
// 	}
}

void NSFederationSystem::ResultSetGuildEmblem(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spUpdateGuildEmblem DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketSetGuildEmblemNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpUpdateGuildEmblem>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketSetGuildEmblemNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketSetGuildEmblemNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	pGuild->SetEmblem(cData->ToNPGuildEmblem());
//
// 	NSPacketSetGuildEmblemAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetEmblem(cData->ToNPGuildEmblem());
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketSetGuildProfileReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketSetGuildProfileReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
// 	auto profile = pcReq->GetProfile();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpUpdateGuildProfile>(guildId, cid, profile);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpUpdateGuildProfile>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultSetGuildProfile));
// 	}
}

void NSFederationSystem::ResultSetGuildProfile(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spUpdateGuildProfile DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketSetGuildProfileNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpUpdateGuildProfile>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketSetGuildProfileNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketSetGuildProfileNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	pGuild->SetProfile(cData->Input.Profile);
//
// 	NSPacketSetGuildProfileAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetProfile(cData->Input.Profile);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketSetGuildAcceptTypeReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketSetGuildAcceptTypeReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
// 	auto acceptType = pcReq->GetAcceptType();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpUpdateGuildAcceptType>(guildId, cid, acceptType);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpUpdateGuildAcceptType>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultSetGuildAcceptType));
// 	}
}

void NSFederationSystem::ResultSetGuildAcceptType(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spUpdateGuildAcceptType DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketSetGuildAcceptTypeNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpUpdateGuildAcceptType>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketSetGuildAcceptTypeNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketSetGuildAcceptTypeNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto acceptType = from_ENpLib_GuildAcceptType(cData->Input.AcceptType);
// 	pGuild->SetAcceptType(acceptType);
//
// 	NSPacketSetGuildAcceptTypeAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetAcceptType(static_cast<uint8_t>(cData->Input.AcceptType));
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketSetGuildAcceptLimitReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketSetGuildAcceptLimitReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
// 	auto acceptLevel = pcReq->GetAcceptLevel();
// 	auto acceptGearScore = pcReq->GetAcceptGearScore();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpUpdateGuildAcceptLimit>(guildId, cid, acceptLevel, acceptGearScore);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpUpdateGuildAcceptLimit>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultSetGuildAcceptLimit));
// 	}
}

void NSFederationSystem::ResultSetGuildAcceptLimit(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spUpdateGuildAcceptLimit DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketSetGuildAcceptLimitNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpUpdateGuildAcceptLimit>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketSetGuildAcceptLimitNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketSetGuildAcceptLimitNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	pGuild->SetAcceptLimit(cData->Input.AcceptLevel, cData->Input.AcceptGearScore);
//
// 	NSPacketSetGuildAcceptLimitAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetAcceptLevel(cData->Input.AcceptLevel);
// 	cAck.SetAcceptGearScore(cData->Input.AcceptGearScore);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketSetGuildNoticeReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketSetGuildNoticeReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
// 	auto notice = pcReq->GetNotice();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpUpdateGuildNotice>(guildId, cid, notice);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpUpdateGuildNotice>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultSetGuildNotice));
// 	}
}

void NSFederationSystem::ResultSetGuildNotice(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spUpdateGuildNotice DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketSetGuildNoticeNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpUpdateGuildNotice>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketSetGuildNoticeNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketSetGuildNoticeNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
// 	pGuild->SetNotice(cData->Input.Notice);
// 	pGuild->AddHistoryFromJson(cData->Output.ResultHistory);
//
// 	NSPacketSetGuildNoticeAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetNotice(cData->Input.Notice);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketGuildInfoReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildInfoReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto ownerCid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpGetGuildInfo>(guildId, ownerCid);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpGetGuildInfo>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultGuildInfo));
// 	}
}

void NSFederationSystem::ResultGuildInfo(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spGetGuildInfo DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketGuildInfoNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpGetGuildInfo>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketGuildInfoNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpGetGuildInfo::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketGuildInfoNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (recordSet->IsEOF())
// 	{
// 		NSPacketGuildInfoNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto guildInfo = ProcessGuildInfo(recordSet);
// 	if (guildInfo.GuildId < 1)
// 	{
// 		NSPacketGuildInfoNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotFound);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	SendGuildInfo(pcSession, guildInfo, cData->Output.IsMember);
}

void NSFederationSystem::SendGuildInfo(std::shared_ptr<NSSession>& pcSession, const NPGuild& guildInfo, bool isMember)
{
// 	if (pcSession == nullptr)
// 		return;
//
// 	NSPacketGuildInfoAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuild(guildInfo);
// 	cAck.SetIsMember(isMember);
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketGuildHistoryReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildHistoryReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto ownerCid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketGuildHistoryNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (!pGuild->IsGuildMember(ownerCid))
// 	{
// 		NSPacketGuildHistoryNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildMemberInvalidPermission);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (pGuild->IsHistoryLoaded())
// 	{
// 		return SendGuildHistory(pcSession, pGuild);
// 	}
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpGetGuildHistory>(guildId);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpGetGuildHistory>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultGuildHistory));
// 	}
}

void NSFederationSystem::ResultGuildHistory(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spGetGuildHistory DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketGuildHistoryNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpGetGuildHistory>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketGuildHistoryNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpGetGuildHistory::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketGuildHistoryNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketGuildHistoryNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	while (!recordSet->IsEOF())
// 	{
// 		NPGuildHistory guildHistory;
// 		recordSet->GetItem("HistoryUid", guildHistory.HistoryUid);
// 		uint8_t historyType = 0;
// 		recordSet->GetItem("Type", historyType);
// 		guildHistory.Type = from_ENpLib_GuildHistoryType(historyType);
// 		recordSet->GetItem("CID", guildHistory.Cid);
// 		recordSet->GetItem("Value1", guildHistory.Value1);
// 		recordSet->GetItem("Name", guildHistory.Name);
// 		recordSet->GetItem("CreateAt", guildHistory.CreateAt);
// 		pGuild->AddHistory(guildHistory);
// 	}
//
// 	pGuild->SetHistoryLoaded(true);
// 	SendGuildHistory(pcSession, pGuild);
}

void NSFederationSystem::SendGuildHistory(std::shared_ptr<NSSession>& pcSession, std::shared_ptr<NSGuild>& pGuild)
{
// 	if (pcSession == nullptr || pGuild == nullptr)
// 		return;
//
// 	if (!pGuild->IsHistoryLoaded())
// 	{
// 		NSPacketGuildHistoryNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	NSPacketGuildHistoryAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	if (pGuild->GetNPGuildHistories().empty())
// 	{
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 		return;
// 	}
//
// 	for (auto& [_, history] : pGuild->GetNPGuildHistories())
// 	{
// 		if (!cAck.AddHistory(history))
// 		{
// 			NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 			cAck.ClearHistory();
// 			cAck.AddHistory(history);
// 		}
// 	}
//
// 	if (!cAck.GetHistory().empty())
// 	{
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 	}
}

void NSFederationSystem::PacketGuildMemberListReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildMemberListReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto ownerCid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketGuildMemberListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (!pGuild->IsGuildMember(ownerCid))
// 	{
// 		NSPacketGuildMemberListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildMemberInvalidPermission);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (pGuild->IsMemberLoaded())
// 	{
// 		return SendGuildMember(pcSession, pGuild);
// 	}
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpGetGuildMember>(guildId);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpGetGuildMember>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultGuildMemberList));
// 	}
}

void NSFederationSystem::ResultGuildMemberList(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spGetGuildMember DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketGuildMemberListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpGetGuildMember>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketGuildMemberListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpGetGuildMember::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketGuildMemberListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketGuildMemberListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	while (!recordSet->IsEOF())
// 	{
// 		auto memberInfo = ProcessGuildMember(recordSet);
// 		if (!pGuild->AddMember(memberInfo))
// 		{
// 			LOGE << fmt::format("Failed to add guild member. GuildId: {}, Cid: {}",
// 				cData->Input.GuildId, memberInfo.Cid);
// 		}
// 	}
//
// 	pGuild->SetMemberLoaded(true);
// 	SendGuildMember(pcSession, pGuild);
}

void NSFederationSystem::SendGuildMember(std::shared_ptr<NSSession>& pcSession, std::shared_ptr<NSGuild>& pGuild)
{
// 	if (pcSession == nullptr || pGuild == nullptr)
// 		return;
//
// 	if (!pGuild->IsMemberLoaded())
// 	{
// 		NSPacketGuildMemberListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	NSPacketGuildMemberListAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	if (pGuild->GetNPGuildMembers().empty())
// 	{
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!cAck.AddMembers(member))
// 		{
// 			NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 			cAck.ClearMembers();
// 			cAck.AddMembers(member);
// 		}
// 	}
//
// 	if (!cAck.GetMembers().empty())
// 	{
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 	}
}

void NSFederationSystem::PacketGuildWaitingListReq(NSPacket* pcPacket)
{
// 	static const auto validDay = NSGameConfigTemplate::GetInstance()->GetGuildJoinRequestValidDay();
// 	auto pcReq = static_cast<NSPacketGuildWaitingListReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto ownerCid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketGuildWaitingListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (!pGuild->IsGuildMember(ownerCid))
// 	{
// 		NSPacketGuildWaitingListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildMemberInvalidPermission);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpGetGuildWaiting>(guildId, validDay);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpGetGuildWaiting>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultGuildWaitingList));
// 	}
}

void NSFederationSystem::ResultGuildWaitingList(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spGetGuildWaiting DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketGuildWaitingListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpGetGuildWaiting>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketGuildWaitingListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpGetGuildWaiting::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketGuildWaitingListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketGuildWaitingListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pcModule = GetGameModule<NSCommunityModule>();
// 	if (pcModule == nullptr)
// 	{
// 		LOGE << fmt::format("CommunityModule is null");
// 		return;
// 	}
//
// 	bool isEmpty = true;
// 	NSPacketGuildWaitingListAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	while (!recordSet->IsEOF())
// 	{
// 		isEmpty = false;
// 		NPGuildWaiting waitingInfo;
// 		recordSet->GetItem("AID", waitingInfo.Aid);
// 		recordSet->GetItem("CID", waitingInfo.Cid);
// 		recordSet->GetItem("Level", waitingInfo.Level);
// 		int32_t classType = 0;
// 		recordSet->GetItem("ClassType", classType);
// 		waitingInfo.ClassType = from_ENpLib_CharacterClassType(classType);
// 		recordSet->GetItem("Name", waitingInfo.Name);
// 		recordSet->GetItem("Comment", waitingInfo.Comment);
//
// 		if (!cAck.AddWaitings(waitingInfo))
// 		{
// 			NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 			cAck.ClearWaitings();
// 			cAck.AddWaitings(waitingInfo);
// 		}
// 	}
//
// 	if (isEmpty || !cAck.GetWaitings().empty())
// 	{
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 	}
}

void NSFederationSystem::PacketJoinGuildReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketJoinGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto wid = pcReq->GetWID();
// 	auto guildId = pcReq->GetGuildId();
// 	auto gearScore = pcReq->GetGearScore();
// 	auto memberInfo = pcReq->GetMember();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpJoinGuild>(wid, guildId, gearScore, memberInfo);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpJoinGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultJoinGuild));
// 	}
}

void NSFederationSystem::ResultJoinGuild(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spJoinGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpJoinGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet();
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (recordSet->IsEOF())
// 	{
// 		NSPacketJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto guildInfo = ProcessGuildInfo(recordSet);
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildInfo.GuildId);
// 	if (pGuild != nullptr)
// 	{
// 		pGuild->AddHistoryFromJson(cData->Output.ResultHistory);
// 	}
//
// 	NSPacketJoinGuildAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetJoinType(cData->Output.JoinType);
// 	cAck.SetGuild(guildInfo);
// 	cAck.SetMember(cData->ToNPGuildMember());
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketFreeJoinGuildReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketFreeJoinGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto memberInfo = pcReq->GetMember();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild != nullptr)
// 	{
// 		if (!pGuild->AddMember(memberInfo))
// 		{
// 			LOGE << fmt::format("Failed to add guild member. GuildId: {}, Cid: {}",
// 				guildId, memberInfo.Cid);
// 		}
//
// 		SendGuildInfo(pcSession, pGuild->ToNPGuild(), true);
// 		SendGuildMember(pcSession, pGuild);
// 		SendGuildHistory(pcSession, pGuild);
// 		GuildMemberAddedBroadcast(guildId, memberInfo.Cid);
// 		GuildMemberOnlineBroadcast(guildId, memberInfo.Cid, pcSession->GetAID());
// 		return;
// 	}
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		auto batch = dataSerializer.Create<NSStoredProcedureBatch>();
// 		batch->AddStoredProcedure(dataSerializer.Create<SpGetGuildInfo>(guildId, memberInfo.Cid));
// 		batch->AddStoredProcedure(dataSerializer.Create<SpGetGuildMember>(guildId));
// 		batch->AddStoredProcedure(dataSerializer.Create<SpGetGuildHistory>(guildId));
// 		NSDataBaseManager::GetInstance()->PushQuery<NSStoredProcedureBatch>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultFreeJoinGuild));
// 	}
}

void NSFederationSystem::ResultFreeJoinGuild(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		NSPacketFreeJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	//auto cData = pcQueryData->GetQueryData().Front<NSStoredProcedureBatch>();
// 	pcQueryData->GetQueryData().Next(); // TODO: [Austin]
//
// 	//auto cData = pcQueryData->GetQueryData().Front<SpGetGuildInfo>();
// 	auto cDataGuildInfo = pcQueryData->GetQueryData().Front<SpGetGuildInfo>();
// 	if (cDataGuildInfo == nullptr)
// 	{
// 		NSPacketFreeJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto ownerCid = cDataGuildInfo->Input.Cid;
// 	pcQueryData->GetQueryData().Next(); // TODO: [Austin]
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpGetGuildInfo::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketFreeJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (recordSet->IsEOF())
// 	{
// 		NSPacketFreeJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto guildInfo = ProcessGuildInfo(recordSet);
// 	NSGuildManager::GetInstance()->AddGuild(guildInfo);
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildInfo.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketFreeJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotFound);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	SendGuildInfo(pcSession, pGuild->ToNPGuild(), true);
// 	//auto cData = pcQueryData->GetQueryData().Front<SpGetGuildMember>();
// 	ResultGuildMemberList(pcQueryData);
// 	pcQueryData->GetQueryData().Next(); // TODO: [Austin]
//
// 	//auto cData = pcQueryData->GetQueryData().Front<SpGetGuildHistory>();
// 	ResultGuildHistory(pcQueryData);
//
// 	GuildMemberAddedBroadcast(guildInfo.GuildId, ownerCid);
// 	GuildMemberOnlineBroadcast(guildInfo.GuildId, ownerCid, pcSession->GetAID());
}

void NSFederationSystem::PacketCancelJoinGuildReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketCancelJoinGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpCancelJoinGuild>(guildId, cid);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpCancelJoinGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultCancelJoinGuild));
// 	}
}

void NSFederationSystem::ResultCancelJoinGuild(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spCancelJoinGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketCancelJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpCancelJoinGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketCancelJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	NSPacketCancelJoinGuildAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetCID(cData->Input.Cid);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketJoinGuildWaitingListReq(NSPacket* pcPacket)
{
// 	static const auto validDay = NSGameConfigTemplate::GetInstance()->GetGuildJoinRequestValidDay();
// 	auto pcReq = static_cast<NSPacketJoinGuildWaitingListReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto ownerCid = pcReq->GetCID();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpGetGuildJoinWaiting>(ownerCid, validDay);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpGetGuildJoinWaiting>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultJoinGuildWaitingList));
// 	}
}

void NSFederationSystem::ResultJoinGuildWaitingList(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spGetGuildJoinWaiting DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketJoinGuildWaitingListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpGetGuildJoinWaiting>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketJoinGuildWaitingListNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpGetGuildJoinWaiting::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		LOGD << fmt::format("spGetGuildJoinWaiting recordset is empty");
// 		return;
// 	}
//
// 	bool isEmpty = true;
// 	NSPacketJoinGuildWaitingListAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	while (!recordSet->IsEOF())
// 	{
// 		isEmpty = false;
// 		NPGuild guildInfo;
// 		//guildInfo.Wid = cData->Input.Wid;
// 		recordSet->GetItem("GuildID", guildInfo.GuildId);
// 		recordSet->GetItem("Level", guildInfo.Level);
// 		uint8_t acceptType = 0;
// 		recordSet->GetItem("MemberAccept", acceptType);
// 		guildInfo.AcceptType = from_ENpLib_GuildAcceptType(acceptType);
// 		recordSet->GetItem("AcceptLevel", guildInfo.AcceptLevel);
// 		recordSet->GetItem("AcceptGearScore", guildInfo.AcceptGearScore);
// 		recordSet->GetItem("CurrentMember", guildInfo.CurMember);
// 		recordSet->GetItem("MaxMember", guildInfo.MaxMember);
// 		recordSet->GetItem("Name", guildInfo.Name);
// 		recordSet->GetItem("MasterName", guildInfo.MasterName);
// 		recordSet->GetItem("Profile", guildInfo.Profile);
//
// 		recordSet->GetItem("Emblem", guildInfo.Emblem.Symbol);
// 		//recordSet->GetItem("EmblemColor", guildInfo.Emblem.SymbolColor);
// 		//recordSet->GetItem("EmblemBG", guildInfo.Emblem.Background);
// 		//recordSet->GetItem("EmblemBGColor", guildInfo.Emblem.BackgroundColor);
//
// 		if (!cAck.AddGuilds(guildInfo))
// 		{
// 			NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 			cAck.ClearGuilds();
// 			cAck.AddGuilds(guildInfo);
// 		}
// 	}
//
// 	if (isEmpty || !cAck.GetGuilds().empty())
// 	{
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 	}
}

void NSFederationSystem::PacketApproveJoinGuildReq(NSPacket* pcPacket)
{
// 	static const auto validDay = NSGameConfigTemplate::GetInstance()->GetGuildJoinRequestValidDay();
// 	auto pcReq = static_cast<NSPacketApproveJoinGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetApproverCID();
// 	auto guildId = pcReq->GetGuildId();
// 	auto waitingCid = pcReq->GetWaitingCID();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketApproveJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpApproveJoinGuild>(guildId, cid, waitingCid, validDay);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpApproveJoinGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultApproveJoinGuild));
// 	}
}

void NSFederationSystem::ResultApproveJoinGuild(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spApproveJoinGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketApproveJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpApproveJoinGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketApproveJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet();
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketApproveJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (recordSet->IsEOF())
// 	{
// 		NSPacketApproveJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketApproveJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto newMember = ProcessGuildMember(recordSet);
// 	if (!pGuild->AddMember(newMember))
// 	{
// 		LOGE << fmt::format("Failed to add guild member. GuildId: {}, Cid: {}",
// 			cData->Input.GuildId, newMember.Cid);
// 	}
// 	pGuild->AddHistoryFromJson(cData->Output.ResultHistory);
//
// 	NSPacketApproveJoinGuildAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetWaitingCID(cData->Input.WaitingCid);
// 	cAck.SetMember(newMember);
// 	cAck.SetGuildName(pGuild->GetGuildName());
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketRejectJoinGuildReq(NSPacket* pcPacket)
{
// 	static const auto validDay = NSGameConfigTemplate::GetInstance()->GetGuildJoinRequestValidDay();
// 	auto pcReq = static_cast<NSPacketRejectJoinGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetApproverCID();
// 	auto guildId = pcReq->GetGuildId();
// 	auto waitingCid = pcReq->GetWaitingCID();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpRejectJoinGuild>(guildId, cid, waitingCid, validDay);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpRejectJoinGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultRejectJoinGuild));
// 	}
}

void NSFederationSystem::ResultRejectJoinGuild(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spRejectJoinGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketRejectJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpRejectJoinGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketRejectJoinGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	NSPacketRejectJoinGuildAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetWaitingCID(cData->Input.WaitingCid);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketInviteGuildReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketInviteGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto wid = pcReq->GetWID();
// 	auto cid = pcReq->GetSenderCID();
// 	auto guildId = pcReq->GetGuildId();
// 	auto receiverCid = pcReq->GetReceiverCID();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpInviteGuild>(wid, cid, receiverCid, guildId);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpInviteGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultInviteGuild));
// 	}
}

void NSFederationSystem::ResultInviteGuild(NSQueryData* pcQueryData)
{
// 	static const auto timeLimitId = NSGameConfigTemplate::GetInstance()->GetGuildInviteAnswerTimeLimitId();
// 	static const auto timeLimit = NSContentCooldownTemplate::GetInstance()->GetContentCooldownSec(timeLimitId) * 1000;
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spInviteGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketInviteGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpInviteGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketInviteGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpInviteGuild::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketInviteGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (recordSet->IsEOF())
// 	{
// 		NSPacketInviteGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	uint64_t RequestedAt = 0;
// 	recordSet->GetItem("RequestedAt", RequestedAt);
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketInviteGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	NSPacketInviteGuildAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuild(pGuild->ToNPGuild());
// 	cAck.SetSenderCID(cData->Input.Cid);
// 	cAck.SetReceiverCID(cData->Input.ReceiverCid);
// 	cAck.SetExpiredAt(RequestedAt + timeLimit);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketAcceptGuildReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketAcceptGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetReceiverCID();
// 	auto guildId = pcReq->GetGuildId();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpAcceptGuild>(guildId, cid);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpAcceptGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultAcceptGuild));
// 	}
}

void NSFederationSystem::ResultAcceptGuild(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spAcceptGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketAcceptGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpAcceptGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketAcceptGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet();
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketAcceptGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (recordSet->IsEOF())
// 	{
// 		NSPacketAcceptGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketAcceptGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto newMember = ProcessGuildMember(recordSet);
// 	if (!pGuild->AddMember(newMember))
// 	{
// 		LOGE << fmt::format("Failed to add guild member. GuildId: {}, Cid: {}",
// 			cData->Input.GuildId, newMember.Cid);
// 	}
// 	pGuild->AddHistoryFromJson(cData->Output.ResultHistory);
//
// 	int64_t senderCid = 0;
// 	recordSet->GetItem("SenderCID", senderCid);
//
// 	NSPacketAcceptGuildAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetReceiverCID(newMember.Cid);
// 	cAck.SetReceiverName(newMember.Name);
// 	cAck.SetSenderCID(senderCid);
// 	cAck.SetGuild(pGuild->ToNPGuild());
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketDeclineGuildReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketDeclineGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetReceiverCID();
// 	auto guildId = pcReq->GetGuildId();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpDeclineGuild>(guildId, cid);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpDeclineGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultDeclineGuild));
// 	}
}

void NSFederationSystem::ResultDeclineGuild(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spDeclineGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketDeclineGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpDeclineGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketDeclineGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpDeclineGuild::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketDeclineGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (recordSet->IsEOF())
// 	{
// 		NSPacketDeclineGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	int64_t senderCid = 0;
// 	char receiverName[g_uMaxCharacterNameUTF8Length] = {};
// 	recordSet->GetItem("SenderCID", senderCid);
// 	recordSet->GetItem("Name", receiverName);
//
// 	NSPacketDeclineGuildAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetReceiverCID(cData->Input.ReceiverCid);
// 	cAck.SetSenderCID(senderCid);
// 	cAck.SetReceiverName(receiverName);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketDeclineGuildByTimeoutReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketDeclineGuildByTimeoutReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetReceiverCID();
// 	auto guildId = pcReq->GetGuildId();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpDeclineGuild>(guildId, cid);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpDeclineGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultDeclineGuildByTimeout));
// 	}
}

void NSFederationSystem::ResultDeclineGuildByTimeout(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spDeclineGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketDeclineGuildByTimeoutNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpDeclineGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketDeclineGuildByTimeoutNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpDeclineGuild::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketDeclineGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (recordSet->IsEOF())
// 	{
// 		NSPacketDeclineGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	int64_t senderCid = 0;
// 	char receiverName[g_uMaxCharacterNameUTF8Length] = {};
// 	recordSet->GetItem("SenderCID", senderCid);
// 	recordSet->GetItem("Name", receiverName);
//
// 	NSPacketDeclineGuildByTimeoutAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetReceiverCID(cData->Input.ReceiverCid);
// 	cAck.SetSenderCID(senderCid);
// 	cAck.SetReceiverName(receiverName);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketLeaveGuildReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketLeaveGuildReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpLeaveGuild>(guildId, cid);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpLeaveGuild>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultLeaveGuild));
// 	}
}

void NSFederationSystem::ResultLeaveGuild(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spLeaveGuild DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketLeaveGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpLeaveGuild>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketLeaveGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketLeaveGuildNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (!pGuild->RemoveMember(cData->Input.Cid, false))
// 	{
// 		LOGE << fmt::format("Failed to remove guild member. GuildId: {}, Cid: {}",
// 			cData->Input.GuildId, cData->Input.Cid);
// 	}
// 	pGuild->AddHistoryFromJson(cData->Output.ResultHistory);
//
// 	NSPacketLeaveGuildAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetCID(cData->Input.Cid);
// 	cAck.SetPenaltyTimestamp(cData->Output.LeaveTimestamp);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketKickGuildMemberReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketKickGuildMemberReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
// 	auto kickCid = pcReq->GetKickCID();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpKickGuildMember>(guildId, cid, kickCid);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpKickGuildMember>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultKickGuildMember));
// 	}
// }
//
// void NSFederationSystem::ResultKickGuildMember(NSQueryData* pcQueryData)
// {
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spKickGuildMember DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketKickGuildMemberNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpKickGuildMember>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketKickGuildMemberNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketKickGuildMemberNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (!pGuild->RemoveMember(cData->Input.KickCid, true))
// 	{
// 		LOGE << fmt::format("Failed to remove guild member. GuildId: {}, Cid: {}",
// 			cData->Input.GuildId, cData->Input.KickCid);
// 	}
// 	pGuild->AddHistoryFromJson(cData->Output.ResultHistory);
//
// 	NSPacketKickGuildMemberAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetKickCID(cData->Input.KickCid);
// 	cAck.SetPenaltyTimestamp(cData->Output.LeaveTimestamp);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketSetGuildMemberGradeReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketSetGuildMemberGradeReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
// 	auto memberCid = pcReq->GetMemberCID();
// 	auto newGrade = pcReq->GetNewGrade();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketSetGuildMemberGradeNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto member = pGuild->FindMember(memberCid);
// 	if (member == nullptr)
// 	{
// 		NSPacketSetGuildMemberGradeNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildMemberNotFound);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto oldGrade = static_cast<uint8_t>(member->GetGrade());
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpUpdateGuildMemberGrade>(guildId, cid, memberCid, oldGrade, newGrade);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpUpdateGuildMemberGrade>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultSetGuildMemberGrade));
// 	}
}

void NSFederationSystem::ResultSetGuildMemberGrade(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spUpdateGuildMemberGrade DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketSetGuildMemberGradeNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpUpdateGuildMemberGrade>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketSetGuildMemberGradeNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketSetGuildMemberGradeNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
// 	pGuild->SetMemberGrade(cData->Input.MemberCid, cData->Input.NewGrade);
// 	pGuild->AddHistoryFromJson(cData->Output.ResultHistory);
//
// 	NSPacketSetGuildMemberGradeAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetMemberCID(cData->Input.MemberCid);
// 	cAck.SetNewGrade(cData->Input.NewGrade);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketTransferGuildMasterReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketTransferGuildMasterReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto cid = pcReq->GetCID();
// 	auto newMasterCid = pcReq->GetNewMasterCID();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpTransferGuildMaster>(guildId, cid, newMasterCid);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpTransferGuildMaster>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultTransferGuildMaster));
// 	}
}

void NSFederationSystem::ResultTransferGuildMaster(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spTransferGuildMaster DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketTransferGuildMasterNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpTransferGuildMaster>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketTransferGuildMasterNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketTransferGuildMasterNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
// 	pGuild->SetNewGuildMaster(cData->Input.NewMasterCid);
// 	pGuild->AddHistoryFromJson(cData->Output.ResultHistory);
//
// 	NSPacketTransferGuildMasterAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetCID(cData->Input.Cid);
// 	cAck.SetNewMasterCID(cData->Input.NewMasterCid);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketSetGuildMemberCommentReq(NSPacket* pcPacket)
{
//	auto pcReq = static_cast<NSPacketSetGuildMemberCommentReq*>(pcPacket);
//
// 	auto pcSession = NSSessionManager::GetInstance()->GetSessionFromPacket(pcReq);
// 	if (pcSession == nullptr)
// 	{
// 		// disconnected user
// 		return;
// 	}
//
// 	auto cid = pcReq->GetCID();
// 	auto guildId = pcReq->GetGuildId();
// 	auto comment = pcReq->GetComment();
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpUpdateGuildMemberComment>(guildId, cid, comment);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpUpdateGuildMemberComment>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultSetGuildMemberComment));
// 	}
}

void NSFederationSystem::ResultSetGuildMemberComment(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spUpdateGuildMemberComment DB Error[{}]", pcQueryData->GetErrorCode());
//
// 		NSPacketSetGuildMemberCommentNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpUpdateGuildMemberComment>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketSetGuildMemberCommentNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		NSPacketSetGuildMemberCommentNak cNak;
// 		cNak.SetSessionByAID(pcSession->GetAID());
// 		cNak.SetResult(EErrorCode::GuildNotLoaded);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
// 	pGuild->SetMemberComment(cData->Input.MemberCid, cData->Input.Comment);
//
// 	NSPacketSetGuildMemberCommentAck cAck;
// 	cAck.SetSessionByAID(pcSession->GetAID());
// 	cAck.SetGuildId(cData->Input.GuildId);
// 	cAck.SetCID(cData->Input.MemberCid);
// 	cAck.SetComment(cData->Input.Comment);
//
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

void NSFederationSystem::PacketSetGuildLevelReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketSetGuildLevelReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto level = pcReq->GetLevel();
// 	auto maxMember = NSGuildTemplate::GetInstance()->GetMaxPersonnelByLevel(level);
//
// 	if (guildId < 1)
// 	{
// 		LOGW << fmt::format("Invalid GuildId. {}", guildId);
// 		return;
// 	}
//
// 	if (level < 1)
// 	{
// 		LOGW << fmt::format("Invalid Guild level. {}", level);
// 		return;
// 	}
//
// 	if (maxMember < 1)
// 	{
// 		LOGW << fmt::format("Invalid Guild MaxMember. {}", maxMember);
// 		return;
// 	}
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		SpSetGuildLevel guildLevel(guildId, level, maxMember);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpSetGuildLevel>(__FUNCTIONW__, __LINE__, 0, guildLevel, RESULT_FUNC(this, &NSFederationSystem::ResultSetGuildLevel));
// 	}
}

void NSFederationSystem::ResultSetGuildLevel(NSQueryData* pcQueryData)
{
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spGmCmdSetGuildLevel DB Error[{}]", pcQueryData->GetErrorCode());
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpSetGuildLevel>();
// 	if (cData == nullptr)
// 	{
// 		LOGW << fmt::format("spGmCmdSetGuildLevel DB Error. cData is null");
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("spGmCmdSetGuildLevel DB Error. guild is not loaded. guildId: {}", cData->Input.GuildId);
// 		return;
// 	}
// 	pGuild->SetLevel(cData->Input.Level);
// 	pGuild->SetMaxMember(cData->Input.MaxMember);
//
// 	GuildLevelBroadcast(cData->Input.GuildId);
}

void NSFederationSystem::ProcessNewGuildMasterByAfk(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spGetNewGuildMasterByAfk DB Error[{}]", pcQueryData->GetErrorCode());
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpGetNewGuildMasterByAfk>();
// 	if (cData == nullptr)
// 	{
// 		LOGW << fmt::format("spGetNewGuildMasterByAfk DB Error. cData is null");
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpGetNewGuildMasterByAfk::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		LOGW << fmt::format("spGetNewGuildMasterByAfk DB Error. recordSet is null");
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("ProcessNewGuildMasterByAfk Error. pGuild is null");
// 		return;
// 	}
//
// 	auto guildId = cData->Input.GuildId;
// 	auto oldMasterCid = cData->Output.OldMasterCid;
// 	auto newMasterCid = cData->Output.NewMasterCid;
//
// 	if (newMasterCid < 1)
// 	{
// 		// no new master candidate available.
// 		return;
// 	}
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		dataSerializer.Create<SpTransferGuildMaster>(guildId, oldMasterCid, newMasterCid);
// 		NSDataBaseManager::GetInstance()->PushQuery<SpTransferGuildMaster>(__FUNCTIONW__, __LINE__, pcSession, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultNewGuildMasterByAfk));
// 	}
}

void NSFederationSystem::ResultNewGuildMasterByAfk(NSQueryData* pcQueryData)
{
// 	auto pcSession = pcQueryData->GetSession();
// 	assert(pcSession != nullptr);
//
// 	if (!pcQueryData->IsValid())
// 	{
// 		LOGW << fmt::format("spTransferGuildMaster DB Error[{}] (AFK)", pcQueryData->GetErrorCode());
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpTransferGuildMaster>();
// 	if (cData == nullptr)
// 	{
// 		LOGW << fmt::format("spTransferGuildMaster DB Error. cData is null");
// 		return;
// 	}
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(cData->Input.GuildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("ResultNewGuildMasterByAfk Error. pGuild is null");
// 		return;
// 	}
// 	pGuild->SetNewGuildMaster(cData->Input.NewMasterCid);
//
// 	auto guildId = cData->Input.GuildId;
// 	auto oldMasterCid = cData->Input.Cid;
// 	auto newMasterCid = cData->Input.NewMasterCid;
//
// 	NSCommunityServerConnector::GetInstance()->BroadcastGuildMasterTransferReq(guildId, oldMasterCid, newMasterCid);
}

void NSFederationSystem::PacketGuildMemberOnlineBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildMemberOnlineBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto loginCid = pcReq->GetLoginCID();
// 	auto loginAid = pcReq->GetLoginAID();
//
// 	GuildMemberOnlineBroadcast(guildId, loginCid, loginAid);
}

void NSFederationSystem::GuildMemberOnlineBroadcast(const int64_t guildId, const int64_t loginCid, const int64_t loginAid)
{
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to login guild member");
// 		return;
// 	}
// 	pGuild->LoginMember(loginCid);
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (member.Cid == loginCid)
// 			continue;
//
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildMemberOnlineBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetLoginCID(loginCid);
// 		cNtf.SetLoginAID(loginAid);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildMemberOfflineBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildMemberOfflineBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto logoutCid = pcReq->GetLogoutCID();
// 	auto logoutAid = pcReq->GetLogoutAID();
//
// 	GuildMemberOfflineBroadcast(guildId, logoutCid, logoutAid);
}

void NSFederationSystem::GuildMemberOfflineBroadcast(const int64_t guildId, const int64_t logoutCid, const int64_t logoutAid)
{
// 	auto lastActivity = NSTickManager::GetInstance()->GetUnixTimeStamp();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to logout guild member");
// 		return;
// 	}
// 	pGuild->LogoutMember(logoutCid);
//
// 	if (pGuild->IsAllMemberOffline())
// 	{
// 		NSGuildManager::GetInstance()->RemoveGuild(guildId);
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (member.Cid == logoutCid)
// 			continue;
//
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildMemberOfflineBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetLogoutCID(logoutCid);
// 		cNtf.SetLogoutAID(logoutAid);
// 		cNtf.SetLastActivity(lastActivity);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildMemberAddedBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildMemberAddedBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto addedCid = pcReq->GetAddedCID();
//
// 	GuildMemberAddedBroadcast(guildId, addedCid);
}

void NSFederationSystem::GuildMemberAddedBroadcast(const int64_t guildId, const int64_t addedCid)
{
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to added guild member");
// 		return;
// 	}
//
// 	auto newMember = pGuild->FindMember(addedCid);
// 	if (newMember == nullptr)
// 	{
// 		LOGW << fmt::format("Guild #{}, #{} is not the guild member", guildId, addedCid);
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (member.Cid == addedCid)
// 			continue;
//
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildMemberAddedBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetNewMember(newMember->ToNPGuildMember());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildMemberLeftBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildMemberLeftBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto leftCid = pcReq->GetLeftCID();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to left guild member");
// 		return;
// 	}
//
// 	auto leftMember = pGuild->FindMember(leftCid);
// 	if (leftMember != nullptr)
// 	{
// 		LOGW << fmt::format("Guild #{}, #{} is still a guild member", guildId, leftCid);
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildMemberLeftBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetLeftCID(leftCid);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildMemberKickedBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildMemberKickedBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto kickedCid = pcReq->GetKickedCID();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to left guild member");
// 		return;
// 	}
//
// 	auto kickedMember = pGuild->FindMember(kickedCid);
// 	if (kickedMember != nullptr)
// 	{
// 		LOGW << fmt::format("Guild #{}, #{} is still a guild member", guildId, kickedCid);
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildMemberKickedBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetKickedCID(kickedCid);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildWaitingMemberAddedBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildWaitingMemberAddedBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto waitingMember = pcReq->GetWaitingMember();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to add a waiting");
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildWaitingMemberAddedBroadcastNtf cNtf;
// 		cNtf.SetWaitingMember(waitingMember);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildWaitingMemberRemovedBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildWaitingMemberRemovedBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto waitingCid = pcReq->GetWaitingCID();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to remove the waiting");
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildWaitingMemberRemovedBroadcastNtf cNtf;
// 		cNtf.SetWaitingCID(waitingCid);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildEmblemBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildEmblemBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to sync emblem");
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildEmblemBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetEmblem(pGuild->ToNPGuildEmblem());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildNoticeBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildNoticeBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to sync notice");
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildNoticeBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetNotice(pGuild->GetNotice());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildProfileBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildProfileBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to sync profile");
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildProfileBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetProfile(pGuild->GetProfile());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildAcceptTypeBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildAcceptTypeBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to sync accept type");
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildAcceptTypeBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetAcceptType(static_cast<uint8_t>(pGuild->GetAcceptType()));
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildAcceptLimitBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildAcceptLimitBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to sync accept limit");
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildAcceptLimitBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetAcceptLevel(pGuild->GetAcceptLevel());
// 		cNtf.SetAcceptGearScore(pGuild->GetAcceptGearScore());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildMemberGradeBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildMemberGradeBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto memberCid = pcReq->GetMemberCID();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to sync member grade");
// 		return;
// 	}
//
// 	auto affectedMember = pGuild->FindMember(memberCid);
// 	if (affectedMember == nullptr)
// 	{
// 		LOGW << fmt::format("Guild #{}, #{} is invalid member", guildId, memberCid);
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildMemberGradeBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetMember(affectedMember->ToNPGuildMember());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildMemberCommentBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildMemberCommentBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto memberCid = pcReq->GetMemberCID();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to sync member comment");
// 		return;
// 	}
//
// 	auto affectedMember = pGuild->FindMember(memberCid);
// 	if (affectedMember == nullptr)
// 	{
// 		LOGW << fmt::format("Guild #{}, #{} is invalid member", guildId, memberCid);
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildMemberCommentBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetMemberCID(memberCid);
// 		cNtf.SetComment(affectedMember->GetComment());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildMasterTransferBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildMasterTransferBroadcastReq*>(pcPacket);
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto oldMasterCid = pcReq->GetOldMasterCID();
// 	auto newMasterCid = pcReq->GetNewMasterCID();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to sync the new master");
// 		return;
// 	}
//
// 	auto affectedMember = pGuild->FindMember(newMasterCid);
// 	if (affectedMember == nullptr)
// 	{
// 		LOGW << fmt::format("Guild #{}, #{} is invalid member", guildId, newMasterCid);
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildMasterTransferBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetOldMasterCID(oldMasterCid);
// 		cNtf.SetNewMaster(affectedMember->ToNPGuildMember());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketGuildLevelBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildLevelBroadcastReq*>(pcPacket);
// 	auto guildId = pcReq->GetGuildId();
//
// 	GuildLevelBroadcast(guildId);
}

void NSFederationSystem::PacketGuildChatBroadcastReq(NSPacket* pcPacket)
{
// 	auto pcReq = static_cast<NSPacketGuildChatBroadcastReq*>(pcPacket);
// 	auto guildId = pcReq->GetGuildId();
// 	decltype(auto) pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to sync the guild chat");
// 		return;
// 	}
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildChatBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetSender(pcReq->GetSender());
// 		cNtf.SetGuildId(guildId);
// 		cNtf.SetContents(pcReq->GetContents());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::GuildLevelBroadcast(const int64_t guildId)
{
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild == nullptr)
// 	{
// 		LOGW << fmt::format("Guild is not loaded but requested to sync the guild level");
// 		return;
// 	}
//
// 	auto level = pGuild->GetLevel();
// 	auto maxMember = pGuild->GetMaxMember();
//
// 	for (auto& member : pGuild->GetNPGuildMembers())
// 	{
// 		if (!member.IsOnline)
// 			continue;
//
// 		NSPacketGuildLevelBroadcastNtf cNtf;
// 		cNtf.SetSessionByCID(member.Cid);
// 		cNtf.SetLevel(level);
// 		cNtf.SetMaxMember(maxMember);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNtf);
// 	}
}

void NSFederationSystem::PacketSearchGuildInfoReq(std::shared_ptr<NSSession>& session, NSPacket* pcPacket)
{
	[[maybe_unused]] auto pcReq = static_cast<NSPacketSearchGuildInfoReq*>(pcPacket);

	NSPacketSearchGuildInfoAck newPacket;
	session->Send(newPacket);
//
// 	auto guildId = pcReq->GetGuildId();
// 	auto callbackId = pcReq->GetCallbackId();
//
// 	auto pGuild = NSGuildManager::GetInstance()->FindGuild(guildId);
// 	if (pGuild != nullptr)
// 	{
// 		NSPacketSearchGuildInfoAck cAck;
// 		cAck.SetResult(EErrorCode::None);
// 		cAck.SetGuild(pGuild->ToNPGuild());
// 		cAck.SetCallbackId(callbackId);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
// 		return;
// 	}
//
// 	{
// 		NSDataSerializer dataSerializer;
// 		auto searchGuild = dataSerializer.Create<SpSearchGuildInfo>(guildId);
// 		if (searchGuild != nullptr)
// 		{
// 			searchGuild->Data.CallbackId = callbackId;
// 		}
// 		NSDataBaseManager::GetInstance()->PushQuery<SpSearchGuildInfo>(__FUNCTIONW__, __LINE__, DB_SHARD_KEY, dataSerializer, RESULT_FUNC(this, &NSFederationSystem::ResultSearchGuildInfo));
// 	}
}

void NSFederationSystem::ResultSearchGuildInfo(NSQueryData* pcQueryData)
{
// 	if (!pcQueryData->IsValid())
// 	{
// 		NSPacketSearchGuildInfoNak cNak;
// 		cNak.SetResult(pcQueryData->GetErrorCode());
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto cData = pcQueryData->GetQueryData().Front<SpSearchGuildInfo>();
// 	if (cData == nullptr)
// 	{
// 		NSPacketSearchGuildInfoNak cNak;
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto recordSet = pcQueryData->GetAdoRecordSet(SpSearchGuildInfo::GetName());
// 	if (recordSet == nullptr)
// 	{
// 		NSPacketSearchGuildInfoNak cNak;
// 		cNak.SetResult(EErrorCode::DBError);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	if (recordSet->IsEOF())
// 	{
// 		NSPacketSearchGuildInfoNak cNak;
// 		cNak.SetResult(EErrorCode::GuildNotFound);
// 		NSCommunityServerConnector::GetInstance()->MockupHandler(&cNak);
// 		return;
// 	}
//
// 	auto guildInfo = ProcessGuildInfo(recordSet);
// 	NSPacketSearchGuildInfoAck cAck;
// 	cAck.SetResult(EErrorCode::None);
// 	cAck.SetGuild(guildInfo);
// 	cAck.SetCallbackId(cData->Data.CallbackId);
// 	NSCommunityServerConnector::GetInstance()->MockupHandler(&cAck);
}

NPGuild NSFederationSystem::ProcessGuildInfo(NSAdoRecordset* recordSet)
{
 	NPGuild guildInfo{};
// 	recordSet->GetItem("GuildID", guildInfo.GuildId);
// 	recordSet->GetItem("WID", guildInfo.Wid);
// 	recordSet->GetItem("Exp", guildInfo.Exp);
// 	recordSet->GetItem("Level", guildInfo.Level);
// 	recordSet->GetItem("Faction", guildInfo.Faction);
// 	recordSet->GetItem("Name", guildInfo.Name);
// 	recordSet->GetItem("MasterCid", guildInfo.MasterCid);
// 	recordSet->GetItem("MasterName", guildInfo.MasterName);
// 	recordSet->GetItem("Profile", guildInfo.Profile);
// 	recordSet->GetItem("Notice", guildInfo.Notice);
// 	recordSet->GetItem("Emblem", guildInfo.Emblem.Symbol);
// 	//recordSet->GetItem("EmblemColor", guildInfo.Emblem.SymbolColor);
// 	//recordSet->GetItem("EmblemBG", guildInfo.Emblem.Background);
// 	//recordSet->GetItem("EmblemBGColor", guildInfo.Emblem.BackgroundColor);
// 	uint8_t acceptType = 0;
// 	recordSet->GetItem("MemberAccept", acceptType);
// 	guildInfo.AcceptType = from_ENpLib_GuildAcceptType(acceptType);
// 	recordSet->GetItem("AcceptLevel", guildInfo.AcceptLevel);
// 	recordSet->GetItem("AcceptGearScore", guildInfo.AcceptGearScore);
// 	recordSet->GetItem("CurrentMember", guildInfo.CurMember);
// 	recordSet->GetItem("MaxMember", guildInfo.MaxMember);
//
 	return guildInfo;
}

NPGuildMember NSFederationSystem::ProcessGuildMember(NSAdoRecordset* recordSet)
{
	NPGuildMember guildMember{};

// 	auto pcModule = GetGameModule<NSCommunityModule>();
// 	if (pcModule == nullptr)
// 	{
// 		LOGE << fmt::format("CommunityModule is null");
// 		return guildMember;
// 	}
//
// 	recordSet->GetItem("AID", guildMember.Aid);
// 	recordSet->GetItem("CID", guildMember.Cid);
// 	recordSet->GetItem("Level", guildMember.Level);
// 	guildMember.IsOnline = pcModule->IsCharacterOnline(guildMember.Cid);
// 	if (!guildMember.IsOnline)
// 	{
// 		recordSet->GetItem("LastActivity", guildMember.LastActivity);
// 	}
// 	int32_t classType = 0;
// 	recordSet->GetItem("ClassType", classType);
// 	guildMember.ClassType = from_ENpLib_CharacterClassType(classType);
// 	recordSet->GetItem("MemberName", guildMember.Name);
// 	recordSet->GetItem("RegionID", guildMember.RegionId);
// 	FVector pos = {};
// 	recordSet->GetItem("PosX", pos.X);
// 	recordSet->GetItem("PosY", pos.Y);
// 	recordSet->GetItem("PosZ", pos.Z);
// 	guildMember.Location = NSConvertUtil::ToNPVec3(pos);
// 	uint8_t memberGrade = 0;
// 	recordSet->GetItem("Grade", memberGrade);
// 	guildMember.Grade = from_ENpLib_GuildMemberGrade(memberGrade);
// 	recordSet->GetItem("Comment", guildMember.Comment);

	return guildMember;
}