﻿#include "stdafx.h"
#include "SpUpdateGuildNotice.h"

#include "ADO/NSAdoCommand.h"

SpUpdateGuildNotice::SpUpdateGuildNotice(const int64_t GuildId, const int64_t Cid, const char* Notice) :
	Input{ GuildId, Cid, }, Output{}
{
	memcpy_s(Input.Notice, sizeof(Input.Notice), Notice, strlen(Notice) + 1);
}

EErrorCode SpUpdateGuildNotice::MakeQuery(NSAdoCommand* command)
{
	// TODO: [Austin] Sanitize the notice to prevent sql injection
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("CID", Input.Cid);
	if (!command->SetItem("Notice", Input.Notice))
		return EErrorCode::DBArgumentError;

	return EErrorCode::None;
}

EErrorCode SpUpdateGuildNotice::MakeOutput(NSAdoCommand* command)
{
	command->GetItem("ResultHistory", Output.ResultHistory, 4000);

	return EErrorCode::None;
}

//EErrorCode SpUpdateGuildNotice::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}
