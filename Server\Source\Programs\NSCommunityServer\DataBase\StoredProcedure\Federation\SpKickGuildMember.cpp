﻿#include "stdafx.h"
#include "SpKickGuildMember.h"

#include "ADO/NSAdoCommand.h"

SpKickGuildMember::SpKickGuildMember(const int64_t GuildId, const int64_t Cid, const int64_t KickCid) :
	Input{ GuildId, Cid, KickCid }, Output{}
{
}

EErrorCode SpKickGuildMember::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("CID", Input.Cid);
	command->SetItem("KickCID", Input.KickCid);

	return EErrorCode::None;
}

EErrorCode SpKickGuildMember::MakeOutput(NSAdoCommand* command)
{
	command->GetItem("LeaveTimestamp", Output.LeaveTimestamp);
	command->GetItem("ResultHistory", Output.ResultHistory, 4000);

	return EErrorCode::None;
}

//EErrorCode SpKickGuildMember::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}
