#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpKickGuildMember : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spKickGuildMember";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int64_t GuildId = 0;
		int64_t Cid = 0;
		int64_t KickCid = 0;
	} Input;

	struct Output
	{
		uint64_t LeaveTimestamp = 0;
		char ResultHistory[4001] = {};
	} Output;

	SpKickGuildMember() = default;
	SpKickGuildMember(const int64_t GuildId, const int64_t Cid, const int64_t KickCid);
};
