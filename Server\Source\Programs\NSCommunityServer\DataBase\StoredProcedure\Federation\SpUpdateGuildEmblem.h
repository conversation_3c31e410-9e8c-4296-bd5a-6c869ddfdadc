#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpUpdateGuildEmblem : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spUpdateGuildEmblem";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	//EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input : NPGuildEmblem
	{
		int64_t GuildId = 0;
		int64_t Cid = 0;
	} Input;

	SpUpdateGuildEmblem() = default;
	SpUpdateGuildEmblem(const int64_t GuildId, const int64_t Cid, const NPGuildEmblem& Emblem);
	NPGuildEmblem ToNPGuildEmblem() const;
};
