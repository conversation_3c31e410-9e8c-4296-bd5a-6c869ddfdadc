# DBModel 저장 프로시저 목록

## CommonDB 저장 프로시저 (25개)

### 캐릭터 월드 관리
- [spDeleteCharacterWorld](Server/DBModel/commondb/spDeleteCharacterWorld.sql)
- [spInsertCharacterWorld](Server/DBModel/commondb/spInsertCharacterWorld.sql)
- [spSelectCharacterWorlds](Server/DBModel/commondb/spSelectCharacterWorlds.sql)

### GM 서버 관리
- [spGMBatchUpdateMaintenanceStatus](Server/DBModel/commondb/spGMBatchUpdateMaintenanceStatus.sql)
- [spGMCancelMaintenance](Server/DBModel/commondb/spGMCancelMaintenance.sql)
- [spGMCreateServer](Server/DBModel/commondb/spGMCreateServer.sql)
- [spGMDeleteServer](Server/DBModel/commondb/spGMDeleteServer.sql)
- [spGMGetMaintenanceServers](Server/DBModel/commondb/spGMGetMaintenanceServers.sql)
- [spGMGetNoticeByWid](Server/DBModel/commondb/spGMGetNoticeByWid.sql)
- [spGMGetServerInfo](Server/DBModel/commondb/spGMGetServerInfo.sql)
- [spGMGetServerInfoByWid](Server/DBModel/commondb/spGMGetServerInfoByWid.sql)
- [spGMGetServerList](Server/DBModel/commondb/spGMGetServerList.sql)
- [spGMInsertNotice](Server/DBModel/commondb/spGMInsertNotice.sql)
- [spGMUpdateMaintenanceStatus](Server/DBModel/commondb/spGMUpdateMaintenanceStatus.sql)
- [spGMUpdateMaintenanceStatusByWid](Server/DBModel/commondb/spGMUpdateMaintenanceStatusByWid.sql)
- [spGMUpdateServer](Server/DBModel/commondb/spGMUpdateServer.sql)
- [spGMUpdateServerDisplayOrder](Server/DBModel/commondb/spGMUpdateServerDisplayOrder.sql)
- [spGMUpsertMaintenanceStatus](Server/DBModel/commondb/spGMUpsertMaintenanceStatus.sql)
- [spGMUpsertServer](Server/DBModel/commondb/spGMUpsertServer.sql)

### 공통 데이터 관리
- [spInsertDataBaseError](Server/DBModel/commondb/spInsertDataBaseError.sql)
- [spInsertReport](Server/DBModel/commondb/spInsertReport.sql)
- [spSelectAccountInfo](Server/DBModel/commondb/spSelectAccountInfo.sql)
- [spSelectMaintenance](Server/DBModel/commondb/spSelectMaintenance.sql)
- [spSelectNotices](Server/DBModel/commondb/spSelectNotices.sql)
- [spSelectServerInfo](Server/DBModel/commondb/spSelectServerInfo.sql)

## GameDB 저장 프로시저 (약 200개)

### 계정 관리 (Account)
- [spLoginAccount](Server/DBModel/gamedb/spLoginAccount.sql)
- [spLogoutAccount](Server/DBModel/gamedb/spLogoutAccount.sql)

### 캐릭터 관리 (Character)
- [spCreateCharacter](Server/DBModel/gamedb/spCreateCharacter.sql)
- [spDeleteCharacter](Server/DBModel/gamedb/spDeleteCharacter.sql)
- [spDeleteCharacterImmediate](Server/DBModel/gamedb/spDeleteCharacterImmediate.sql)
- [spCancelDeleteCharacter](Server/DBModel/gamedb/spCancelDeleteCharacter.sql)
- [spCheckCharacterName](Server/DBModel/gamedb/spCheckCharacterName.sql)
- [spSelectCharacter](Server/DBModel/gamedb/spSelectCharacter.sql)
- [spSelectCharacterData](Server/DBModel/gamedb/spSelectCharacterData.sql)
- [spUpdateCharacter](Server/DBModel/gamedb/spUpdateCharacter.sql)
- [spUpsertCharacterData](Server/DBModel/gamedb/spUpsertCharacterData.sql)
- [spGetCharacterList](Server/DBModel/gamedb/spGetCharacterList.sql)
- [spGetCharacterCustomize](Server/DBModel/gamedb/spGetCharacterCustomize.sql)
- [spGetCharacterCustomizeListForLobby](Server/DBModel/gamedb/spGetCharacterCustomizeListForLobby.sql)
- [spGetCharacterRegionId](Server/DBModel/gamedb/spGetCharacterRegionId.sql)
- [spSearchCharacterName](Server/DBModel/gamedb/spSearchCharacterName.sql)
- [spSearchPlayer](Server/DBModel/gamedb/spSearchPlayer.sql)

### 캐릭터 데이터 (Character Data)
- [spGetCharacterPoints](Server/DBModel/gamedb/spGetCharacterPoints.sql)
- [spUpsertCharacterPoints](Server/DBModel/gamedb/spUpsertCharacterPoints.sql)
- [spGetCharacterStable](Server/DBModel/gamedb/spGetCharacterStable.sql)
- [spUpsertCharacterStable](Server/DBModel/gamedb/spUpsertCharacterStable.sql)
- [spGetCharacterMonsterSoul](Server/DBModel/gamedb/spGetCharacterMonsterSoul.sql)
- [spUpsertCharacterMonsterSoul](Server/DBModel/gamedb/spUpsertCharacterMonsterSoul.sql)
- [spGetCharacterMatrixContents](Server/DBModel/gamedb/spGetCharacterMatrixContents.sql)
- [spUpsertCharacterMatrixContents](Server/DBModel/gamedb/spUpsertCharacterMatrixContents.sql)
- [spGetCharacterMatrixContentsCores](Server/DBModel/gamedb/spGetCharacterMatrixContentsCores.sql)
- [spSelectCharacterGoods](Server/DBModel/gamedb/spSelectCharacterGoods.sql)
- [spUpsertCharacterGoods](Server/DBModel/gamedb/spUpsertCharacterGoods.sql)
- [spSelectCharacterUniquePerk](Server/DBModel/gamedb/spSelectCharacterUniquePerk.sql)
- [spUpsertCharacterUniquePerk](Server/DBModel/gamedb/spUpsertCharacterUniquePerk.sql)
- [spSelectCharacterDungeonRecord](Server/DBModel/gamedb/spSelectCharacterDungeonRecord.sql)
- [spUpsertCharacterDungeonRecord](Server/DBModel/gamedb/spUpsertCharacterDungeonRecord.sql)

### 아이템 관리 (Item)
- [spGetItem](Server/DBModel/gamedb/spGetItem.sql)
- [spGetItemAccountStorage](Server/DBModel/gamedb/spGetItemAccountStorage.sql)
- [spGetItemOption](Server/DBModel/gamedb/spGetItemOption.sql)
- [spGetItemOptionAccountStorage](Server/DBModel/gamedb/spGetItemOptionAccountStorage.sql)
- [spUpsertCharacterItem](Server/DBModel/gamedb/spUpsertCharacterItem.sql)
- [spUpsertItemAccountStorage](Server/DBModel/gamedb/spUpsertItemAccountStorage.sql)
- [spUpsertItemCraft](Server/DBModel/gamedb/spUpsertItemCraft.sql)
- [spUpsertInventory](Server/DBModel/gamedb/spUpsertInventory.sql)
- [spGetEquipment](Server/DBModel/gamedb/spGetEquipment.sql)
- [spGetEquipmentListForLobby](Server/DBModel/gamedb/spGetEquipmentListForLobby.sql)

### 길드 관리 (Guild)
- [spCreateGuild](Server/DBModel/gamedb/spCreateGuild.sql)
- [spCheckGuildName](Server/DBModel/gamedb/spCheckGuildName.sql)
- [spJoinGuild](Server/DBModel/gamedb/spJoinGuild.sql)
- [spApproveJoinGuild](Server/DBModel/gamedb/spApproveJoinGuild.sql)
- [spRejectJoinGuild](Server/DBModel/gamedb/spRejectJoinGuild.sql)
- [spCancelJoinGuild](Server/DBModel/gamedb/spCancelJoinGuild.sql)
- [spLeaveGuild](Server/DBModel/gamedb/spLeaveGuild.sql)
- [spKickGuildMember](Server/DBModel/gamedb/spKickGuildMember.sql)
- [spDismissGuild](Server/DBModel/gamedb/spDismissGuild.sql)
- [spInviteGuild](Server/DBModel/gamedb/spInviteGuild.sql)
- [spAcceptGuild](Server/DBModel/gamedb/spAcceptGuild.sql)
- [spDeclineGuild](Server/DBModel/gamedb/spDeclineGuild.sql)
- [spTransferGuildMaster](Server/DBModel/gamedb/spTransferGuildMaster.sql)
- [spGetGuildInfo](Server/DBModel/gamedb/spGetGuildInfo.sql)
- [spGetGuildList](Server/DBModel/gamedb/spGetGuildList.sql)
- [spGetGuildMember](Server/DBModel/gamedb/spGetGuildMember.sql)
- [spGetGuildWaiting](Server/DBModel/gamedb/spGetGuildWaiting.sql)
- [spGetGuildJoinWaiting](Server/DBModel/gamedb/spGetGuildJoinWaiting.sql)
- [spGetRecommendGuild](Server/DBModel/gamedb/spGetRecommendGuild.sql)
- [spSearchGuild](Server/DBModel/gamedb/spSearchGuild.sql)
- [spSearchGuildInfo](Server/DBModel/gamedb/spSearchGuildInfo.sql)
- [spGetNewGuildMasterByAfk](Server/DBModel/gamedb/spGetNewGuildMasterByAfk.sql)

### 길드 설정 (Guild Settings)
- [spUpdateGuildProfile](Server/DBModel/gamedb/spUpdateGuildProfile.sql)
- [spUpdateGuildNotice](Server/DBModel/gamedb/spUpdateGuildNotice.sql)
- [spUpdateGuildEmblem](Server/DBModel/gamedb/spUpdateGuildEmblem.sql)
- [spUpdateGuildMemberGrade](Server/DBModel/gamedb/spUpdateGuildMemberGrade.sql)
- [spUpdateGuildMemberComment](Server/DBModel/gamedb/spUpdateGuildMemberComment.sql)
- [spUpdateGuildAcceptLimit](Server/DBModel/gamedb/spUpdateGuildAcceptLimit.sql)
- [spUpdateGuildAcceptType](Server/DBModel/gamedb/spUpdateGuildAcceptType.sql)
- [spUpdateGuildSiegeStarTime](Server/DBModel/gamedb/spUpdateGuildSiegeStarTime.sql)

### 길드 금고 (Guild Coffer)
- [spDepositGuildCoffer](Server/DBModel/gamedb/spDepositGuildCoffer.sql)
- [spWithdrawGuildCoffer](Server/DBModel/gamedb/spWithdrawGuildCoffer.sql)
- [spGetGuildCoffer](Server/DBModel/gamedb/spGetGuildCoffer.sql)
- [spUpdateGuildCofferWithdrawLimit](Server/DBModel/gamedb/spUpdateGuildCofferWithdrawLimit.sql)
- [spGuildCofferResetWithdraw](Server/DBModel/gamedb/spGuildCofferResetWithdraw.sql)
- [spGetGuildHistory](Server/DBModel/gamedb/spGetGuildHistory.sql)

### 친구 관리 (Friend)
- [spInviteFriend](Server/DBModel/gamedb/spInviteFriend.sql)
- [spAcceptFriend](Server/DBModel/gamedb/spAcceptFriend.sql)
- [spDeclineFriend](Server/DBModel/gamedb/spDeclineFriend.sql)
- [spCancelInviteFriend](Server/DBModel/gamedb/spCancelInviteFriend.sql)
- [spDeleteFriend](Server/DBModel/gamedb/spDeleteFriend.sql)
- [spGetFriendList](Server/DBModel/gamedb/spGetFriendList.sql)
- [spGetInviteIncoming](Server/DBModel/gamedb/spGetInviteIncoming.sql)
- [spGetInviteOutgoing](Server/DBModel/gamedb/spGetInviteOutgoing.sql)

### 거래/마켓 (Deal/Market)
- [spRegisterDeal](Server/DBModel/gamedb/spRegisterDeal.sql)
- [spRegisterDealEscrow](Server/DBModel/gamedb/spRegisterDealEscrow.sql)
- [spBuyDeal](Server/DBModel/gamedb/spBuyDeal.sql)
- [spBuyDealPartial](Server/DBModel/gamedb/spBuyDealPartial.sql)
- [spCancelDeal](Server/DBModel/gamedb/spCancelDeal.sql)
- [spCancelDealEscrow](Server/DBModel/gamedb/spCancelDealEscrow.sql)
- [spPayDeal](Server/DBModel/gamedb/spPayDeal.sql)
- [spSettleDeal](Server/DBModel/gamedb/spSettleDeal.sql)
- [spSettleDealEscrow](Server/DBModel/gamedb/spSettleDealEscrow.sql)
- [spSoldDeal](Server/DBModel/gamedb/spSoldDeal.sql)
- [spReserveDeal](Server/DBModel/gamedb/spReserveDeal.sql)
- [spReserveDealNew](Server/DBModel/gamedb/spReserveDealNew.sql)
- [spRevertDeal](Server/DBModel/gamedb/spRevertDeal.sql)
- [spTransferDealItemOwner](Server/DBModel/gamedb/spTransferDealItemOwner.sql)
- [spGetDealByCategory](Server/DBModel/gamedb/spGetDealByCategory.sql)
- [spGetDealByDealId](Server/DBModel/gamedb/spGetDealByDealId.sql)
- [spGetDealByItemId](Server/DBModel/gamedb/spGetDealByItemId.sql)
- [spGetDealByItemIdMulti](Server/DBModel/gamedb/spGetDealByItemIdMulti.sql)
- [spGetDealOption](Server/DBModel/gamedb/spGetDealOption.sql)
- [spGetUserDeal](Server/DBModel/gamedb/spGetUserDeal.sql)
- [spGetUserDealHistory](Server/DBModel/gamedb/spGetUserDealHistory.sql)
- [spCheckUserDeal](Server/DBModel/gamedb/spCheckUserDeal.sql)
- [spCleanUpDeal](Server/DBModel/gamedb/spCleanUpDeal.sql)
- [spCleanUpDealEscrow](Server/DBModel/gamedb/spCleanUpDealEscrow.sql)
- [spDeductDealItemQuantity](Server/DBModel/gamedb/spDeductDealItemQuantity.sql)
- [spUpdateUserDeal](Server/DBModel/gamedb/spUpdateUserDeal.sql)
- [spGetTrendByItemId](Server/DBModel/gamedb/spGetTrendByItemId.sql)

### 에스크로 (Escrow)
- [spEscrowDepositCharacterGoods](Server/DBModel/gamedb/spEscrowDepositCharacterGoods.sql)
- [spEscrowDepositCharacterItem](Server/DBModel/gamedb/spEscrowDepositCharacterItem.sql)
- [spEscrowReclaimCharacterGoods](Server/DBModel/gamedb/spEscrowReclaimCharacterGoods.sql)
- [spEscrowReclaimCharacterItem](Server/DBModel/gamedb/spEscrowReclaimCharacterItem.sql)
- [spEscrowWithdrawCharacterGoods](Server/DBModel/gamedb/spEscrowWithdrawCharacterGoods.sql)
- [spEscrowWithdrawCharacterItem](Server/DBModel/gamedb/spEscrowWithdrawCharacterItem.sql)

### 마켓 위시리스트 (Market Wishlist)
- [spAddMarketWishlist](Server/DBModel/gamedb/spAddMarketWishlist.sql)
- [spRemoveMarketWishlist](Server/DBModel/gamedb/spRemoveMarketWishlist.sql)
- [spRemoveMarketMultiWishlist](Server/DBModel/gamedb/spRemoveMarketMultiWishlist.sql)
- [spGetMarketWishlist](Server/DBModel/gamedb/spGetMarketWishlist.sql)
- [spMarketAutoRegist](Server/DBModel/gamedb/spMarketAutoRegist.sql)
- [spClearMarketAutoRegist](Server/DBModel/gamedb/spClearMarketAutoRegist.sql)

### 퀘스트 (Quest)
- [spGetQuestActivate](Server/DBModel/gamedb/spGetQuestActivate.sql)
- [spGetQuestCompleted](Server/DBModel/gamedb/spGetQuestCompleted.sql)
- [spGetQuestInprogress](Server/DBModel/gamedb/spGetQuestInprogress.sql)
- [spGetCompletedQuestSteps](Server/DBModel/gamedb/spGetCompletedQuestSteps.sql)
- [spUpdateQuest](Server/DBModel/gamedb/spUpdateQuest.sql)

### 던전 (Dungeon)
- [spSelectDungeon](Server/DBModel/gamedb/spSelectDungeon.sql)
- [spSelectDungeonMatch](Server/DBModel/gamedb/spSelectDungeonMatch.sql)
- [spUpdateDungeon](Server/DBModel/gamedb/spUpdateDungeon.sql)

### 크로노텍터 (Chronotector)
- [spGetChronotector](Server/DBModel/gamedb/spGetChronotector.sql)
- [spGetChronotectorAction](Server/DBModel/gamedb/spGetChronotectorAction.sql)
- [spGetChronotectorUpgrade](Server/DBModel/gamedb/spGetChronotectorUpgrade.sql)
- [spGetChronotectorUpgradePoint](Server/DBModel/gamedb/spGetChronotectorUpgradePoint.sql)
- [spGetActiveSlot_Chronotector](Server/DBModel/gamedb/spGetActiveSlot_Chronotector.sql)
- [spUpsertChronotectorAction](Server/DBModel/gamedb/spUpsertChronotectorAction.sql)
- [spUpsertChronotectorNode](Server/DBModel/gamedb/spUpsertChronotectorNode.sql)
- [spUpsertChronotectorUpgrade](Server/DBModel/gamedb/spUpsertChronotectorUpgrade.sql)

### 스킬/능력 (Skill/Ability)
- [spGetAbility](Server/DBModel/gamedb/spGetAbility.sql)
- [spUpsertAbility](Server/DBModel/gamedb/spUpsertAbility.sql)
- [spGetMasterySkill](Server/DBModel/gamedb/spGetMasterySkill.sql)
- [spGetWeaponMastery](Server/DBModel/gamedb/spGetWeaponMastery.sql)
- [spUpsertWeaponMastery](Server/DBModel/gamedb/spUpsertWeaponMastery.sql)
- [spActiveSlot_WeaponMastery](Server/DBModel/gamedb/spActiveSlot_WeaponMastery.sql)
- [spGetActiveSlot_WeaponMastery](Server/DBModel/gamedb/spGetActiveSlot_WeaponMastery.sql)
- [spGetSkillCooldown](Server/DBModel/gamedb/spGetSkillCooldown.sql)
- [spUpdateSkillCooldown](Server/DBModel/gamedb/spUpdateSkillCooldown.sql)
- [spGetPassiveCooldown](Server/DBModel/gamedb/spGetPassiveCooldown.sql)
- [spUpdatePassiveCooldown](Server/DBModel/gamedb/spUpdatePassiveCooldown.sql)

### 업적 (Achievement)
- [spGetAchievement](Server/DBModel/gamedb/spGetAchievement.sql)
- [spUpdateAchievement](Server/DBModel/gamedb/spUpdateAchievement.sql)
- [spGetAchievementGroupClearStatus](Server/DBModel/gamedb/spGetAchievementGroupClearStatus.sql)

### 코스튬 (Costume)
- [spSelectCostume](Server/DBModel/gamedb/spSelectCostume.sql)
- [spSelectCostumePreset](Server/DBModel/gamedb/spSelectCostumePreset.sql)
- [spSelectCostumesForLobby](Server/DBModel/gamedb/spSelectCostumesForLobby.sql)
- [spUpdateCostume](Server/DBModel/gamedb/spUpdateCostume.sql)

### 하우스 (House)
- [spGetHouse](Server/DBModel/gamedb/spGetHouse.sql)
- [spUpsertHouse](Server/DBModel/gamedb/spUpsertHouse.sql)

### 설정 (Settings)
- [spGetSettings](Server/DBModel/gamedb/spGetSettings.sql)
- [spUpsertSettings](Server/DBModel/gamedb/spUpsertSettings.sql)

### 감정표현 (Emotion)
- [spGetEmotionConfig](Server/DBModel/gamedb/spGetEmotionConfig.sql)
- [spGetEmotionSlot](Server/DBModel/gamedb/spGetEmotionSlot.sql)
- [spUpdateEmotionConfig](Server/DBModel/gamedb/spUpdateEmotionConfig.sql)
- [spUpdateEmotionSlot](Server/DBModel/gamedb/spUpdateEmotionSlot.sql)
- [spResetEmotionInfo](Server/DBModel/gamedb/spResetEmotionInfo.sql)
- [spResetEmotionInfoByDataID](Server/DBModel/gamedb/spResetEmotionInfoByDataID.sql)

### 차단 (Block)
- [spBlockPlayer](Server/DBModel/gamedb/spBlockPlayer.sql)
- [spUnBlockPlayer](Server/DBModel/gamedb/spUnBlockPlayer.sql)
- [spGetBlockPlayer](Server/DBModel/gamedb/spGetBlockPlayer.sql)

### 컨텐츠 언락 (Content Unlock)
- [spGetContentUnlock](Server/DBModel/gamedb/spGetContentUnlock.sql)
- [spUpsertContentUnlock](Server/DBModel/gamedb/spUpsertContentUnlock.sql)
- [spGetUnlockContents](Server/DBModel/gamedb/spGetUnlockContents.sql)
- [spUpsertUnlockContents](Server/DBModel/gamedb/spUpsertUnlockContents.sql)
- [spGetCraftUnlock](Server/DBModel/gamedb/spGetCraftUnlock.sql)

### 초기화/기타 (Init/Misc)
- [spGetInitContents](Server/DBModel/gamedb/spGetInitContents.sql)
- [spUpsertInitContents](Server/DBModel/gamedb/spUpsertInitContents.sql)
- [spGetCoolTime](Server/DBModel/gamedb/spGetCoolTime.sql)
- [spUpsertCoolTime](Server/DBModel/gamedb/spUpsertCoolTime.sql)
- [spGetReturnPoint](Server/DBModel/gamedb/spGetReturnPoint.sql)
- [spUpsertReturnPoint](Server/DBModel/gamedb/spUpsertReturnPoint.sql)
- [spSelectDocumentProp](Server/DBModel/gamedb/spSelectDocumentProp.sql)
- [spUpsertDocumentProp](Server/DBModel/gamedb/spUpsertDocumentProp.sql)
- [spGetRecentPartyPlayer](Server/DBModel/gamedb/spGetRecentPartyPlayer.sql)
- [spUpdateRecentPartyPlayer](Server/DBModel/gamedb/spUpdateRecentPartyPlayer.sql)
- [spGetStatusEffects](Server/DBModel/gamedb/spGetStatusEffects.sql)
- [spUpdateStatusEffects](Server/DBModel/gamedb/spUpdateStatusEffects.sql)
- [spUpdatePvPFlag](Server/DBModel/gamedb/spUpdatePvPFlag.sql)
- [spGetStoreUsage](Server/DBModel/gamedb/spGetStoreUsage.sql)
- [spUpsertStoreUsage](Server/DBModel/gamedb/spUpsertStoreUsage.sql)
- [spSelectSaveJsonFormatData](Server/DBModel/gamedb/spSelectSaveJsonFormatData.sql)
- [spUpsertSaveJsonFormatData](Server/DBModel/gamedb/spUpsertSaveJsonFormatData.sql)
- [spInitRuby](Server/DBModel/gamedb/spInitRuby.sql)
- [spSelectRuby](Server/DBModel/gamedb/spSelectRuby.sql)
- [spExtractJsonData](Server/DBModel/gamedb/spExtractJsonData.sql)
- [spInsertDataBaseError](Server/DBModel/gamedb/spInsertDataBaseError.sql)
- [spInsertPendingLogEvents](Server/DBModel/gamedb/spInsertPendingLogEvents.sql)
- [spSelectPendingLogEvents](Server/DBModel/gamedb/spSelectPendingLogEvents.sql)
- [spDeletePendingLogEvents](Server/DBModel/gamedb/spDeletePendingLogEvents.sql)
- [spSearchWhipserTarget](Server/DBModel/gamedb/spSearchWhipserTarget.sql)

### 소모품 활성 슬롯 (Consume Item Active Slot)
- [spGetActiveSlot_ConsumeItem](Server/DBModel/gamedb/spGetActiveSlot_ConsumeItem.sql)

### 입양 시스템 (Adopt System)
- [spAdopt_GiveReward](Server/DBModel/gamedb/spAdopt_GiveReward.sql)

### GM 관리 프로시저 (GM Management)
- [spGMAddItemOption](Server/DBModel/gamedb/spGMAddItemOption.sql)
- [spGMCommandCreateItem](Server/DBModel/gamedb/spGMCommandCreateItem.sql)
- [spGMCompleteDocument](Server/DBModel/gamedb/spGMCompleteDocument.sql)
- [spGMCompleteQuest](Server/DBModel/gamedb/spGMCompleteQuest.sql)
- [spGMDeleteBlockPlayer](Server/DBModel/gamedb/spGMDeleteBlockPlayer.sql)
- [spGMDeleteDeal](Server/DBModel/gamedb/spGMDeleteDeal.sql)
- [spGMDeleteFriendship](Server/DBModel/gamedb/spGMDeleteFriendship.sql)
- [spGMDeleteItem](Server/DBModel/gamedb/spGMDeleteItem.sql)
- [spGMExpireDeal](Server/DBModel/gamedb/spGMExpireDeal.sql)
- [spGMGetAccountCharacters](Server/DBModel/gamedb/spGMGetAccountCharacters.sql)
- [spGMGetAccountInfo](Server/DBModel/gamedb/spGMGetAccountInfo.sql)
- [spGMGetCharacterDetails](Server/DBModel/gamedb/spGMGetCharacterDetails.sql)
- [spGMGetCharacterList](Server/DBModel/gamedb/spGMGetCharacterList.sql)
- [spGMGetDealDetails](Server/DBModel/gamedb/spGMGetDealDetails.sql)
- [spGMGetDealList](Server/DBModel/gamedb/spGMGetDealList.sql)
- [spGMGiveItem](Server/DBModel/gamedb/spGMGiveItem.sql)
- [spGMGrantCharacterTitle](Server/DBModel/gamedb/spGMGrantCharacterTitle.sql)
- [spGMGrantSpecialAccount](Server/DBModel/gamedb/spGMGrantSpecialAccount.sql)
- [spGMRecoverCharacter](Server/DBModel/gamedb/spGMRecoverCharacter.sql)
- [spGMRemoveGems](Server/DBModel/gamedb/spGMRemoveGems.sql)
- [spGMResetAchievement](Server/DBModel/gamedb/spGMResetAchievement.sql)
- [spGMResetChronotector](Server/DBModel/gamedb/spGMResetChronotector.sql)
- [spGMResetDocument](Server/DBModel/gamedb/spGMResetDocument.sql)
- [spGMResetSkills](Server/DBModel/gamedb/spGMResetSkills.sql)
- [spGMRevokeSpecialAccount](Server/DBModel/gamedb/spGMRevokeSpecialAccount.sql)
- [spGMSeizeAccountStorageItem](Server/DBModel/gamedb/spGMSeizeAccountStorageItem.sql)
- [spGMSeizeItem](Server/DBModel/gamedb/spGMSeizeItem.sql)
- [spGMUpdateCharacterGold](Server/DBModel/gamedb/spGMUpdateCharacterGold.sql)
- [spGMUpdateCharacterName](Server/DBModel/gamedb/spGMUpdateCharacterName.sql)
- [spGMUpdateCharacterPosition](Server/DBModel/gamedb/spGMUpdateCharacterPosition.sql)
- [spGMUpdateItemQuantity](Server/DBModel/gamedb/spGMUpdateItemQuantity.sql)

### GM 명령어 프로시저 (GM Commands)
- [spGmCmdCreateDummyGuild](Server/DBModel/gamedb/spGmCmdCreateDummyGuild.sql)
- [spGmCmdResetGuildSiegeStartTimeCoolDown](Server/DBModel/gamedb/spGmCmdResetGuildSiegeStartTimeCoolDown.sql)
- [spGmCmdSetCharacterGuildJoinTime](Server/DBModel/gamedb/spGmCmdSetCharacterGuildJoinTime.sql)
- [spGmCmdSetCharacterLogoutTime](Server/DBModel/gamedb/spGmCmdSetCharacterLogoutTime.sql)
- [spGmCmdSetGuildCofferBalance](Server/DBModel/gamedb/spGmCmdSetGuildCofferBalance.sql)

## 함수 (Functions)
- [fnConvertToDateTime](Server/DBModel/gamedb/fnConvertToDateTime.sql)
- [fnConvertToTimestamp](Server/DBModel/gamedb/fnConvertToTimestamp.sql)
- [fnConvertToTimestampSec](Server/DBModel/gamedb/fnConvertToTimestampSec.sql)
- [fnGetTrendId](Server/DBModel/gamedb/fnGetTrendId.sql)
- [fnGetTrendIdDate](Server/DBModel/gamedb/fnGetTrendIdDate.sql)
- [fnGetTrendIdDatePlusOne](Server/DBModel/gamedb/fnGetTrendIdDatePlusOne.sql)

## LogDB 테이블 (Log Database Tables)
- [chatlog](Server/DBModel/logdb/chatlog.sql) - 채팅 로그 테이블
- [gamelog](Server/DBModel/logdb/gamelog.sql) - 게임 로그 테이블
- [systemlog](Server/DBModel/logdb/systemlog.sql) - 시스템 로그 테이블

## 요약
- **CommonDB**: 25개 저장 프로시저 (서버 관리, 공지사항, 계정 정보)
- **GameDB**: 약 200개 저장 프로시저 (게임 핵심 기능)
- **Functions**: 6개 유틸리티 함수
- **LogDB**: 3개 로그 테이블 정의
- **총계**: 약 **250개 이상**의 데이터베이스 객체

모든 프로시저는 MMO 게임 서버의 핵심 기능들을 담당하며, 캐릭터 관리, 길드 시스템, 아이템 거래, 퀘스트, 던전 등의 게임 메커니즘을 지원합니다.
