﻿#include "stdafx.h"
#include "SpLeaveGuild.h"

#include "ADO/NSAdoCommand.h"

SpLeaveGuild::SpLeaveGuild(const int64_t GuildId, const int64_t Cid) :
	Input{ GuildId, Cid }, Output{}
{
}

EErrorCode SpLeaveGuild::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("CID", Input.Cid);

	return EErrorCode::None;
}

EErrorCode SpLeaveGuild::MakeOutput(NSAdoCommand* command)
{
	command->GetItem("LeaveTimestamp", Output.LeaveTimestamp);
	command->GetItem("ResultHistory", Output.ResultHistory, 4000);

	return EErrorCode::None;
}

//EErrorCode SpLeaveGuild::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}
