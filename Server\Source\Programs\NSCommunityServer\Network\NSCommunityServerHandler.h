#pragma once

#include "Session/NSSession.h"
#include "Win32/NSThread.h"

#include "CNLServiceLogicAdapter.h"
#include "NSCommunityServerServiceLogicAdapter.h"

class CNLIOInterfaceAdapter;

class NSPacketData;
class NSConsoleCommand;

class NSCommunityServerHandler : public CNLServiceLogicAdapterRef<NSCommunityServerServiceLogicAdapter>, public TemplateSingleton<NSCommunityServerHandler>, public Promise::NSExecutor
{
public:
	NSCommunityServerHandler();
	virtual ~NSCommunityServerHandler();

public:
	void OnClose(int64_t socketChannelId, const std::string& error);

	virtual void ForwardedEventAccept(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface)														override;
	virtual void ForwardedEventRecvPacket(int64_t socketInterfaceId, const char* buffer, int32_t size, bool allocateForCopy)									override;
	virtual void ForwardedEventClose(int64_t socketInterfaceId, int32_t nativeErrorCode, const std::string& why)										override;

public:
	void Init(int channelPoolCnt);

	void AddConsoleCommand(const wchar_t* consoleCommand);
	const NSConsoleCommand* GetConsoleCommand() const;

public:
	void Start();
	void Stop();
	uint64_t GetCurrentTick();
	uint64_t GetLastElaspsedTick();

	bool RunningInThisThread() const override;
	void Post(const std::function<void()>& function) override;

private:
	void Process();
	void ProcessAccept();
	void ProcessClose();
	void ProcessRecvPacket();
	void ProcessGameLogic();
	void ProcessConsoleCommand();

	void ProcessExecutor();

private:
	concurrency::concurrent_queue<std::shared_ptr<NSSession>>		m_AcceptSessionQueue;
	concurrency::concurrent_queue<int64_t>							m_CloseSessionQueue;
	std::vector<NSPacketData*>										m_ReceivePacketQueue;
	concurrency::concurrent_queue<std::wstring>						m_ConsoleCommandQueue;

	std::atomic<bool>								m_Run{ false };
	NSThread										m_MainThread;

	std::unique_ptr<NSConsoleCommand>				m_ConsoleCommand;

	uint64_t										m_CurrentTick{ 0 };
	uint64_t										m_LastelElapsedTick{ 0 };
	bool											m_MoveServer{ false };

	std::thread::id									m_ThreadId;

	std::mutex										m_ExecutorMutex;
	std::vector<std::function<void()>>				m_ExecutorFrontQueue;
	std::vector<std::function<void()>>				m_ExecutorBackQueue;
};
