#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpGuildCofferResetWithdraw : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spGuildCofferResetWithdraw";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	//EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int64_t GuildId = 0;
		uint64_t NextResetTimestamp = 0;
	} Input;

	SpGuildCofferResetWithdraw() = default;
	SpGuildCofferResetWithdraw(const int64_t GuildId, const uint64_t NextResetTimestamp);
};
