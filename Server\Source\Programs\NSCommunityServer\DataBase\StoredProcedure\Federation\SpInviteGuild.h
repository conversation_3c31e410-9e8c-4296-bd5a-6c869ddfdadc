﻿#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpInviteGuild : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spInviteGuild";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	//EErrorCode MakeOutput(NSAdoCommand* command);


public:
	EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int32_t Wid = 0;
		int64_t Cid = 0;
		int64_t ReceiverCid = 0;
		int64_t GuildId = 0;
	} Input;

	SpInviteGuild() = default;
	SpInviteGuild(const int32_t Wid, const int64_t Cid, const int64_t ReceiverCid, const int64_t GuildId);
};
