#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpTransferGuildMaster : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spTransferGuildMaster";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int64_t GuildId = 0;
		int64_t Cid = 0;
		int64_t NewMasterCid = 0;
	} Input;

	struct Output
	{
		char ResultHistory[4001] = {};
	} Output;

	SpTransferGuildMaster() = default;
	SpTransferGuildMaster(const int64_t GuildId, const int64_t Cid, const int64_t NewMasterCid);
};
