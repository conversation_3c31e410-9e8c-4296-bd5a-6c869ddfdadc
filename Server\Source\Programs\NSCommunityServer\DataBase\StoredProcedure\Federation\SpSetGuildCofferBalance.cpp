﻿#include "stdafx.h"
#include "SpSetGuildCofferBalance.h"

#include "ADO/NSAdoCommand.h"

SpSetGuildCofferBalance::SpSetGuildCofferBalance(const int64_t GuildId, const TYPE_QUANTITY Amount) :
	Input{ GuildId, Amount }
{
}

EErrorCode SpSetGuildCofferBalance::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("Amount", Input.Amount);

	return EErrorCode::None;
}

// EErrorCode SpSetGuildCofferBalance::MakeOutput(NSAdoCommand* command)
// {
// 	command->GetItem("@LeaveTimestamp", Output.LeaveTimestamp);
// 	command->GetItem("@ResultHistory", Output.ResultHistory, 4000);

// 	return EErrorCode::None;
// }

//EErrorCode SpSetGuildCofferBalance::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}
