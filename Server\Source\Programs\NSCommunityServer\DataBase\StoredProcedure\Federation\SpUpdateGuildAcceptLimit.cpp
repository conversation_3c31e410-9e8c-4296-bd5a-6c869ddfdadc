﻿#include "stdafx.h"
#include "SpUpdateGuildAcceptLimit.h"

#include "ADO/NSAdoCommand.h"

SpUpdateGuildAcceptLimit::SpUpdateGuildAcceptLimit(const int64_t GuildId, const int64_t Cid, const int32_t AcceptLevel, const int32_t AcceptGearScore) :
	Input{ GuildId, Cid, AcceptLevel, AcceptGearScore }
{
}

EErrorCode SpUpdateGuildAcceptLimit::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("CID", Input.Cid);
	command->SetItem("AcceptLevel", Input.AcceptLevel);
	command->SetItem("AcceptGearScore", Input.AcceptGearScore);

	return EErrorCode::None;
}

//EErrorCode SpUpdateGuildAcceptLimit::MakeOutput(NSAdoCommand* command)
//{
//	command->GetItem("@InviteUid", Output.InviteUid);
//
//	return EErrorCode::None;
//}

//EErrorCode SpUpdateGuildAcceptLimit::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}
