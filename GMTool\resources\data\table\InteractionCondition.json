{"infos": {"rowcount": 127, "dataHashCode": -1608785355}, "header": {"DataID": "int32", "GroupID": "int32", "ConditionType_1": "ENpLib_InteractionConditionType", "ConditionValue1_1": "int32", "ConditionValue2_1": "int32", "ConditionType_2": "ENpLib_InteractionConditionType", "ConditionValue1_2": "int32", "ConditionValue2_2": "int32", "ConditionType_3": "ENpLib_InteractionConditionType", "ConditionValue1_3": "int32", "ConditionValue2_3": "int32", "ConditionType_4": "ENpLib_InteractionConditionType", "ConditionValue1_4": "int32", "ConditionValue2_4": "int32"}, "rows": [{"DataID": 2, "GroupID": 2, "ConditionType_1": "HasItem", "ConditionValue1_1": 20100101, "ConditionValue2_1": 5, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 3, "GroupID": 3, "ConditionType_1": "HasItemUnder", "ConditionValue1_1": 20100101, "ConditionValue2_1": 5, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 4, "GroupID": 4, "ConditionType_1": "InteractionFlag", "ConditionValue1_1": 1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 5, "GroupID": 5, "ConditionType_1": "StatusEffect", "ConditionValue1_1": 10025, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 6, "GroupID": 6, "ConditionType_1": "TargetStatusEffect", "ConditionValue1_1": 40060, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7, "GroupID": 7, "ConditionType_1": "DeadBody", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 8, "GroupID": 8, "ConditionType_1": "Dying", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 9, "GroupID": 9, "ConditionType_1": "WarBoard", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 10, "GroupID": 10, "ConditionType_1": "Owner<PERSON><PERSON><PERSON>", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 11, "GroupID": 11, "ConditionType_1": "FrontAngle", "ConditionValue1_1": 180, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 12, "GroupID": 12, "ConditionType_1": "WantedMonster", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 13, "GroupID": 13, "ConditionType_1": "Tuning", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 14, "GroupID": 14, "ConditionType_1": "Tuning", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "FrontAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 4000, "GroupID": 4000, "ConditionType_1": "MimicClosed", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 4001, "GroupID": 4001, "ConditionType_1": "InteractionFlag", "ConditionValue1_1": 1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 4002, "GroupID": 4002, "ConditionType_1": "InteractionFlag", "ConditionValue1_1": 2, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 4003, "GroupID": 4003, "ConditionType_1": "InteractionFlag", "ConditionValue1_1": 3, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 4004, "GroupID": 4004, "ConditionType_1": "InteractionFlag", "ConditionValue1_1": 4, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 4005, "GroupID": 4005, "ConditionType_1": "InteractionFlag", "ConditionValue1_1": 5, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 4006, "GroupID": 4006, "ConditionType_1": "InteractionFlag", "ConditionValue1_1": 6, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 4007, "GroupID": 4007, "ConditionType_1": "InteractionFlag", "ConditionValue1_1": 7, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 4008, "GroupID": 4008, "ConditionType_1": "InteractionFlag", "ConditionValue1_1": 8, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 5001, "GroupID": 5001, "ConditionType_1": "StatusEffect", "ConditionValue1_1": 10020, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 5002, "GroupID": 5002, "ConditionType_1": "StatusEffect", "ConditionValue1_1": 10025, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 6001, "GroupID": 6001, "ConditionType_1": "TargetStatusEffect", "ConditionValue1_1": 40060, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7001, "GroupID": 7001, "ConditionType_1": "GuildMaster", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7003, "GroupID": 7003, "ConditionType_1": "TerritoryFaction", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7004, "GroupID": 7004, "ConditionType_1": "DifferentTerritoryFaction", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7005, "GroupID": 7005, "ConditionType_1": "SameTerritoryFaction", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7006, "GroupID": 7006, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 3, "ConditionValue2_1": -1, "ConditionType_2": "FrontAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "Relation", "ConditionValue1_3": 24, "ConditionValue2_3": -1, "ConditionType_4": "DifferentSiegeState", "ConditionValue1_4": 1, "ConditionValue2_4": -1}, {"DataID": 7007, "GroupID": 7007, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 3, "ConditionValue2_1": -1, "ConditionType_2": "FrontAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "Relation", "ConditionValue1_3": 25, "ConditionValue2_3": -1, "ConditionType_4": "DifferentSiegeState", "ConditionValue1_4": 1, "ConditionValue2_4": -1}, {"DataID": 7008, "GroupID": 7008, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 3, "ConditionValue2_1": -1, "ConditionType_2": "BackAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "Relation", "ConditionValue1_3": 24, "ConditionValue2_3": -1, "ConditionType_4": "DifferentSiegeState", "ConditionValue1_4": 1, "ConditionValue2_4": -1}, {"DataID": 7009, "GroupID": 7009, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 3, "ConditionValue2_1": -1, "ConditionType_2": "BackAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "Relation", "ConditionValue1_3": 25, "ConditionValue2_3": -1, "ConditionType_4": "DifferentSiegeState", "ConditionValue1_4": 1, "ConditionValue2_4": -1}, {"DataID": 7010, "GroupID": 7006, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 0, "ConditionValue2_1": -1, "ConditionType_2": "FrontAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7011, "GroupID": 7008, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 0, "ConditionValue2_1": -1, "ConditionType_2": "BackAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7012, "GroupID": 7006, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 1, "ConditionValue2_1": -1, "ConditionType_2": "FrontAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "PvpOn", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7013, "GroupID": 7008, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 1, "ConditionValue2_1": -1, "ConditionType_2": "BackAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7014, "GroupID": 7006, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 2, "ConditionValue2_1": -1, "ConditionType_2": "FrontAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "PvpOn", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7015, "GroupID": 7008, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 2, "ConditionValue2_1": -1, "ConditionType_2": "BackAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7016, "GroupID": 7016, "ConditionType_1": "FrontAngle", "ConditionValue1_1": 180, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7017, "GroupID": 7017, "ConditionType_1": "BackAngle", "ConditionValue1_1": 180, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7018, "GroupID": 7007, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 0, "ConditionValue2_1": -1, "ConditionType_2": "FrontAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7019, "GroupID": 7009, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 0, "ConditionValue2_1": -1, "ConditionType_2": "BackAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7020, "GroupID": 7007, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 1, "ConditionValue2_1": -1, "ConditionType_2": "FrontAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "PvpOn", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7021, "GroupID": 7009, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 1, "ConditionValue2_1": -1, "ConditionType_2": "BackAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7022, "GroupID": 7007, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 2, "ConditionValue2_1": -1, "ConditionType_2": "FrontAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "PvpOn", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 7023, "GroupID": 7009, "ConditionType_1": "SameTerritoryState", "ConditionValue1_1": 2, "ConditionValue2_1": -1, "ConditionType_2": "BackAngle", "ConditionValue1_2": 180, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1000110, "GroupID": 1000110, "ConditionType_1": "FactionCheckQuestStep", "ConditionValue1_1": 103101101, "ConditionValue2_1": -1, "ConditionType_2": "ClosedQuest", "ConditionValue1_2": 1031010, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1000111, "GroupID": 1000111, "ConditionType_1": "FactionCheckQuestStep", "ConditionValue1_1": 103101601, "ConditionValue2_1": -1, "ConditionType_2": "ClosedQuest", "ConditionValue1_2": 1031015, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1000112, "GroupID": 1000112, "ConditionType_1": "FactionCheckQuestStep", "ConditionValue1_1": 103100601, "ConditionValue2_1": -1, "ConditionType_2": "ClosedQuest", "ConditionValue1_2": 1031005, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 10010160, "GroupID": 10010160, "ConditionType_1": "FactionJoinCheck", "ConditionValue1_1": 1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 10010170, "GroupID": 10010170, "ConditionType_1": "FactionJoinCheck", "ConditionValue1_1": 2, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 10010180, "GroupID": 10010180, "ConditionType_1": "FactionJoinCheck", "ConditionValue1_1": 3, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101101010, "GroupID": 101101010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101101010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101106005, "GroupID": 101106005, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101106005, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 11000, "GroupID": 11000, "ConditionType_1": "QuestProp", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101152500, "GroupID": 101152500, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101152500, "ConditionValue2_1": -1, "ConditionType_2": "InProgressQuestStep", "ConditionValue1_2": 101152510, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 15053, "GroupID": 15053, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101100710, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 15058, "GroupID": 15058, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101100340, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 15059, "GroupID": 15059, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101100542, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 25002, "GroupID": 25002, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 80012565, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101153015, "GroupID": 101153015, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101153015, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101153030, "GroupID": 101153030, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101153030, "ConditionValue2_1": -1, "ConditionType_2": "HasItem", "ConditionValue1_2": 200232, "ConditionValue2_2": 1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101212001, "GroupID": 101212001, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101212001, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101215030, "GroupID": 101215030, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101215030, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101215040, "GroupID": 101215040, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101215040, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101217010, "GroupID": 101217010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101217010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101217530, "GroupID": 101217530, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101217530, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101217510, "GroupID": 101217510, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101217510, "ConditionValue2_1": -1, "ConditionType_2": "HasItem", "ConditionValue1_2": 16450114, "ConditionValue2_2": 1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101219020, "GroupID": 101219020, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101219020, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 26004, "GroupID": 26004, "ConditionType_1": "PickUp", "ConditionValue1_1": 1005, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2010000719, "GroupID": 2010000719, "ConditionType_1": "PickUp", "ConditionValue1_1": 1000, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2010000720, "GroupID": 2010000720, "ConditionType_1": "PickUp", "ConditionValue1_1": 1002, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2010000747, "GroupID": 2010000747, "ConditionType_1": "PickUp", "ConditionValue1_1": 1003, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2010000749, "GroupID": 2010000749, "ConditionType_1": "PickUp", "ConditionValue1_1": 1004, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2010000754, "GroupID": 2010000754, "ConditionType_1": "PickUp", "ConditionValue1_1": 1006, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2010000756, "GroupID": 2010000756, "ConditionType_1": "PickUp", "ConditionValue1_1": 1007, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2010000890, "GroupID": 2010000890, "ConditionType_1": "PickUp", "ConditionValue1_1": 1009, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2010000910, "GroupID": 2010000910, "ConditionType_1": "PickUp", "ConditionValue1_1": 1010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2010000950, "GroupID": 2010000950, "ConditionType_1": "PickUp", "ConditionValue1_1": 1011, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000230, "GroupID": 101600510, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101600510, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000231, "GroupID": 101601010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101601010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000232, "GroupID": 101601510, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101601510, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000233, "GroupID": 101602010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101602010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000234, "GroupID": 101602510, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101602510, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000235, "GroupID": 101603010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101603010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000236, "GroupID": 101603510, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101603510, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000237, "GroupID": 101604010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101604010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000238, "GroupID": 101604510, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101604510, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000239, "GroupID": 101605010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101605010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000240, "GroupID": 101605510, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101605510, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000241, "GroupID": 101606010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101606010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000242, "GroupID": 101606510, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101606510, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000243, "GroupID": 101607010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101607010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 1300000244, "GroupID": 101607510, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101607510, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2090039012, "GroupID": 101603510, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101603510, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2090039013, "GroupID": 101603510, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101603510, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2090039019, "GroupID": 101605010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101605010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2090039020, "GroupID": 101605010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101605010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2090039021, "GroupID": 101605010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101605010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2090039022, "GroupID": 101605010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101605010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2090039023, "GroupID": 101605010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101605010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2090039024, "GroupID": 101605010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101605010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 2090039025, "GroupID": 101605010, "ConditionType_1": "InProgressQuestStep", "ConditionValue1_1": 101605010, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600180000, "GroupID": 600180000, "ConditionType_1": "NotiHasItem", "ConditionValue1_1": 14010211, "ConditionValue2_1": 1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600190000, "GroupID": 600190000, "ConditionType_1": "NotiHasItem", "ConditionValue1_1": 14010211, "ConditionValue2_1": 3, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600020000, "GroupID": 600020000, "ConditionType_1": "Matching", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600310000, "GroupID": 600310000, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 60, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600010000, "GroupID": 600010000, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 60, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600010100, "GroupID": 600010100, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 60, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600010200, "GroupID": 600010200, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 60, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600010300, "GroupID": 600010300, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 60, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600011000, "GroupID": 600011000, "ConditionType_1": "NotiHasItem", "ConditionValue1_1": 29020131, "ConditionValue2_1": 1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600011100, "GroupID": 600011100, "ConditionType_1": "NotiHasItem", "ConditionValue1_1": 29020141, "ConditionValue2_1": 1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600011300, "GroupID": 600011300, "ConditionType_1": "NotiHasItem", "ConditionValue1_1": 29020151, "ConditionValue2_1": 1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600030000, "GroupID": 600030000, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 60, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600040010, "GroupID": 600040010, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 40, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600070000, "GroupID": 600070000, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 180, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600110000, "GroupID": 600110000, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 90, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600120000, "GroupID": 600120000, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 120, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600130000, "GroupID": 600130000, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 180, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600140000, "GroupID": 600140000, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 120, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600160000, "GroupID": 600160000, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 120, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600161000, "GroupID": 600161000, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 90, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600260000, "GroupID": 600260000, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 100, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 600270000, "GroupID": 600270000, "ConditionType_1": "HasChronoRemnants", "ConditionValue1_1": 90, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}, {"DataID": 101002, "GroupID": 101002, "ConditionType_1": "None", "ConditionValue1_1": -1, "ConditionValue2_1": -1, "ConditionType_2": "None", "ConditionValue1_2": -1, "ConditionValue2_2": -1, "ConditionType_3": "None", "ConditionValue1_3": -1, "ConditionValue2_3": -1, "ConditionType_4": "None", "ConditionValue1_4": -1, "ConditionValue2_4": -1}]}