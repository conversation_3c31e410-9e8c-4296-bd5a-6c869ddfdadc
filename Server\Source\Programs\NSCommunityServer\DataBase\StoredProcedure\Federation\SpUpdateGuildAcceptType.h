﻿#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpUpdateGuildAcceptType : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spUpdateGuildAcceptType";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	//EErrorCode MakeOutput(NSAdoCommand* command);
	


public:
	EErrorCode HandleReturnValue(const int32_t returnValue);


public:
	struct Input
	{
		int64_t GuildId = 0;
		int64_t Cid = 0;
		uint8_t AcceptType = 0;
	} Input;

	SpUpdateGuildAcceptType() = default;
	SpUpdateGuildAcceptType(const int64_t GuildId, const int64_t Cid, const uint8_t AcceptType);
};
