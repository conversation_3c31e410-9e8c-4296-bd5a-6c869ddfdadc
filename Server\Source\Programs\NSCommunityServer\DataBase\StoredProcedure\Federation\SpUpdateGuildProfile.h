#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"
#include "CommunityServer/NSGuild.h"

class SpUpdateGuildProfile : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spUpdateGuildProfile";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	//EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int64_t GuildId = 0;
		int64_t Cid = 0;
		char Profile[g_uMaxGuildProfileUTF8Length] = {};
	} Input;

	SpUpdateGuildProfile() = default;
	SpUpdateGuildProfile(const int64_t GuildId, const int64_t Cid, const char* Profile);
};
