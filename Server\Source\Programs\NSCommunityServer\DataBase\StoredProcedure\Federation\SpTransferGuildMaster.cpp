﻿#include "stdafx.h"
#include "SpTransferGuildMaster.h"

#include "ADO/NSAdoCommand.h"

SpTransferGuildMaster::SpTransferGuildMaster(const int64_t GuildId, const int64_t Cid, const int64_t NewMasterCid) :
	Input{ GuildId, Cid, NewMasterCid }, Output{}
{
}

EErrorCode SpTransferGuildMaster::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("CID", Input.Cid);
	command->SetItem("NewMasterCID", Input.NewMasterCid);

	return EErrorCode::None;
}

EErrorCode SpTransferGuildMaster::MakeOutput(NSAdoCommand* command)
{
	command->GetItem("ResultHistory", Output.ResultHistory, 4000);

	return EErrorCode::None;
}

//EErrorCode SpTransferGuildMaster::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}
