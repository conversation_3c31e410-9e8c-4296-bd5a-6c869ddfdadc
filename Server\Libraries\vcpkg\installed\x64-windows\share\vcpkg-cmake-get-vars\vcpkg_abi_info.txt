cmake 3.31.2
cmake_get_vars\CMakeLists.txt 3fda8c6435ffe8cb481ac9eda466cc29e3843d0a5e0b11c8158842ea25580225
features core
portfile.cmake bbcf9d6b88dd539617f4cfc1f76acbbe9a6e31fc373d79c1c626278768ea2e2a
ports.cmake 02fb6521157895d67c3b6e17068a30d814bafacd0aaa41b43c9f1ea57b795ace
post_build_checks 2
powershell 7.2.24
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-b444fd50b533d551129c8eabbdb7db5fbc5b5d8f
vcpkg-cmake 27d041c4a6feef9a7c92d70e7dd669564e17a7cf8adafd527162591562fd9ce2
vcpkg-port-config.cmake 4fd4c2e909bbdf069eb3c59b4b847b0b386cdb41840714e12b34b7eff41f9e22
vcpkg.json f69b04b13a19ccd0124aebafe180915143d96b73df98163cb7bd2f9a800a03db
vcpkg_cmake_get_vars.cmake da894e0dafa6ef0acdc641f12b02633f273322d86a6d2e7065dc56134478cea3
