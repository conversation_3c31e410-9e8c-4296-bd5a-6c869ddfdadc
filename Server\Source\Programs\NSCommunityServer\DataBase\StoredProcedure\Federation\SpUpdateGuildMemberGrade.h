#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpUpdateGuildMemberGrade : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spUpdateGuildMemberGrade";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int64_t GuildId = 0;
		int64_t Cid = 0;
		int64_t MemberCid = 0;
		uint8_t OldGrade = 0;
		uint8_t NewGrade = 0;
	} Input;

	struct Output
	{
		char ResultHistory[4001] = {};
	} Output;

	SpUpdateGuildMemberGrade() = default;
	SpUpdateGuildMemberGrade(const int64_t GuildId, const int64_t Cid, const int64_t MemberCid, const uint8_t OldGrade, const uint8_t NewGrade);
};
