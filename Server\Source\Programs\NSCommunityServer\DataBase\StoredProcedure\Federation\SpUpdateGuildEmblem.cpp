﻿#include "stdafx.h"
#include "SpUpdateGuildEmblem.h"

#include "ADO/NSAdoCommand.h"

SpUpdateGuildEmblem::SpUpdateGuildEmblem(const int64_t GuildId, const int64_t Cid, const NPGuildEmblem& Emblem) :
	Input{ Emblem, GuildId, Cid }
{
}

EErrorCode SpUpdateGuildEmblem::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("CID", Input.Cid);
	command->SetItem("Emblem", Input.Symbol);
	command->SetItem("EmblemColor", Input.SymbolColor);
	command->SetItem("EmblemBg", Input.Background);
	command->SetItem("EmblemBgColor", Input.BackgroundColor);

	return EErrorCode::None;
}

//EErrorCode SpUpdateGuildEmblem::MakeOutput(NSAdoCommand* command)
//{
//	command->GetItem("@InviteUid", Output.InviteUid);
//
//	return EErrorCode::None;
//}

//EErrorCode SpUpdateGuildEmblem::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}

NPGuildEmblem SpUpdateGuildEmblem::ToNPGuildEmblem() const
{
	NPGuildEmblem emblem{};

	emblem.Symbol = Input.Symbol;
	emblem.SymbolColor = Input.SymbolColor;
	emblem.Background = Input.Background;
	emblem.BackgroundColor = Input.BackgroundColor;

	return emblem;
}
