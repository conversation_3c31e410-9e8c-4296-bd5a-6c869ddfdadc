#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpSearchGuildInfo : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spSearchGuildInfo";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int64_t GuildId = 0;
	} Input;

	SpSearchGuildInfo() = default;
	SpSearchGuildInfo(const int64_t GuildId);
};
