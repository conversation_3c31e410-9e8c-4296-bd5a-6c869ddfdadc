﻿#include "stdafx.h"
#include "SpUpdateGuildMemberGrade.h"

#include "ADO/NSAdoCommand.h"

SpUpdateGuildMemberGrade::SpUpdateGuildMemberGrade(const int64_t GuildId, const int64_t Cid, const int64_t MemberCid,
	const uint8_t OldGrade, const uint8_t NewGrade) :
	Input{ GuildId, Cid, MemberCid, OldGrade, NewGrade }, Output{}
{
}

EErrorCode SpUpdateGuildMemberGrade::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("CID", Input.Cid);
	command->SetItem("MemberCID", Input.MemberCid);
	command->SetItem("OldGrade", Input.OldGrade);
	command->SetItem("NewGrade", Input.NewGrade);

	return EErrorCode::None;
}

EErrorCode SpUpdateGuildMemberGrade::MakeOutput(NSAdoCommand* command)
{
	command->GetItem("ResultHistory", Output.ResultHistory, 4000);

	return EErrorCode::None;
}

//EErrorCode SpUpdateGuildMemberGrade::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}
