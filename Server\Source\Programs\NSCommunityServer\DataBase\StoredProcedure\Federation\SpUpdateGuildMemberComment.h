#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"
#include "CommunityServer/NSGuild.h"

class SpUpdateGuildMemberComment : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spUpdateGuildMemberComment";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	//EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int64_t GuildId = 0;
		int64_t MemberCid = 0;
		char Comment[g_uMaxGuildMemberCommentUTF8Length] = {};
	} Input;

	SpUpdateGuildMemberComment() = default;
	SpUpdateGuildMemberComment(const int64_t GuildId, const int64_t MemberCid, const char* Comment);
};
