﻿#include "stdafx.h"
#include "SpSearchGuild.h"

#include "ADO/NSAdoCommand.h"

SpSearchGuild::SpSearchGuild(const int32_t Wid, const int32_t Count, const char* Name) :
	Input{ Wid, Count, }
{
	memcpy_s(Input.Name, sizeof(Input.Name), Name, strlen(Name) + 1);
}

EErrorCode SpSearchGuild::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("WID", Input.Wid);
	if (!command->SetItem("Name", Input.Name))
		return EErrorCode::DBArgumentError;
	command->SetItem("Count", Input.Count);

	return EErrorCode::None;
}

//EErrorCode SpSearchGuild::MakeOutput(NSAdoCommand* command)
//{
//	command->GetItem("@InviteUid", Output.InviteUid);
//
//	return EErrorCode::None;
//}

//EErrorCode SpSearchGuild::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}
