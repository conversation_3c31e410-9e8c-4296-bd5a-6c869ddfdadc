﻿#include "stdafx.h"
#include "SpUpdateGuildMemberComment.h"

#include "ADO/NSAdoCommand.h"

SpUpdateGuildMemberComment::SpUpdateGuildMemberComment(const int64_t GuildId, const int64_t MemberCid, const char* Comment) :
	Input{ GuildId, MemberCid, }
{
	memcpy_s(Input.Comment, sizeof(Input.Comment), Comment, strlen(Comment) + 1);
}

EErrorCode SpUpdateGuildMemberComment::MakeQuery(NSAdoCommand* command)
{
	// TODO: [Austin] Sanitize the comment to prevent sql injection
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("MemberCID", Input.MemberCid);
	if (!command->SetItem("Comment", Input.Comment))
		return EErrorCode::DBArgumentError;

	return EErrorCode::None;
}

//EErrorCode SpUpdateGuildMemberComment::MakeOutput(NSAdoCommand* command)
//{
//	command->GetItem("@InviteUid", Output.InviteUid);
//
//	return EErrorCode::None;
//}

//EErrorCode SpUpdateGuildMemberComment::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}
