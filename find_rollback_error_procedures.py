#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

def find_problematic_procedures():
    """
    DBModel 폴더에서 ROLLBACK 후 spInsertDataBaseError를 호출하는 문제가 있는 프로시저들을 찾습니다.
    """
    
    # DBModel 폴더의 모든 SQL 파일 검색
    sql_files = []
    for root, dirs, files in os.walk("Server/DBModel"):
        for file in files:
            if file.endswith('.sql') and file.startswith('sp'):
                sql_files.append(os.path.join(root, file))
    
    problematic_procedures = []
    
    for sql_file in sql_files:
        try:
            with open(sql_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # DECLARE EXIT HANDLER 패턴 찾기
            handler_pattern = r'DECLARE\s+EXIT\s+HANDLER\s+FOR\s+SQLEXCEPTION[^;]*?BEGIN(.*?)END;'
            handlers = re.findall(handler_pattern, content, re.DOTALL | re.IGNORECASE)
            
            for handler in handlers:
                # ROLLBACK과 spInsertDataBaseError 호출 순서 확인
                rollback_pos = handler.lower().find('rollback')
                error_call_pos = handler.lower().find('spinsertdatabaseerror')
                
                if rollback_pos != -1 and error_call_pos != -1 and rollback_pos < error_call_pos:
                    procedure_name = os.path.basename(sql_file).replace('.sql', '')
                    problematic_procedures.append({
                        'file': sql_file,
                        'procedure': procedure_name,
                        'handler_content': handler.strip()
                    })
                    break
                    
        except Exception as e:
            print(f"Error reading {sql_file}: {e}")
    
    return problematic_procedures

def main():
    print("=== ROLLBACK 후 spInsertDataBaseError 호출하는 문제가 있는 프로시저 목록 ===\n")
    
    problematic = find_problematic_procedures()
    
    if not problematic:
        print("문제가 있는 프로시저를 찾지 못했습니다.")
        return
    
    print(f"총 {len(problematic)}개의 문제가 있는 프로시저를 발견했습니다:\n")
    
    # 카테고리별로 분류
    commondb_procs = []
    gamedb_procs = []
    
    for proc in problematic:
        if 'commondb' in proc['file']:
            commondb_procs.append(proc)
        elif 'gamedb' in proc['file']:
            gamedb_procs.append(proc)
    
    if commondb_procs:
        print("## CommonDB 프로시저:")
        for proc in commondb_procs:
            print(f"- {proc['procedure']} ({proc['file']})")
        print()
    
    if gamedb_procs:
        print("## GameDB 프로시저:")
        for proc in gamedb_procs:
            print(f"- {proc['procedure']} ({proc['file']})")
        print()
    
    print("\n=== 문제 상세 내용 ===")
    print("이 프로시저들은 다음과 같은 문제가 있습니다:")
    print("1. ROLLBACK을 먼저 실행한 후 spInsertDataBaseError를 호출")
    print("2. ROLLBACK으로 인해 트랜잭션이 종료되어 spInsertDataBaseError 호출이 실패할 수 있음")
    print("3. 에러 로깅이 제대로 되지 않을 가능성")
    print("\n권장 해결책:")
    print("- spInsertDataBaseError 호출을 ROLLBACK 이전으로 이동")
    print("- 또는 별도의 트랜잭션에서 에러 로깅 수행")

if __name__ == "__main__":
    main()
