#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

def find_problematic_procedures():
    """
    DBModel 폴더에서 ROLLBACK 후 spInsertDataBaseError를 호출하는 문제가 있는 프로시저들을 찾습니다.
    """

    # DBModel 폴더의 모든 SQL 파일 검색
    sql_files = []
    for root, dirs, files in os.walk("Server/DBModel"):
        for file in files:
            if file.endswith('.sql') and file.startswith('sp'):
                sql_files.append(os.path.join(root, file))

    problematic_procedures = []

    for sql_file in sql_files:
        try:
            with open(sql_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # DECLARE EXIT HANDLER 패턴 찾기
            handler_pattern = r'DECLARE\s+EXIT\s+HANDLER\s+FOR\s+SQLEXCEPTION[^;]*?BEGIN(.*?)END;'
            handlers = re.findall(handler_pattern, content, re.DOTALL | re.IGNORECASE)

            for handler in handlers:
                # ROLLBACK과 spInsertDataBaseError 호출 순서 확인
                rollback_pos = handler.lower().find('rollback')
                error_call_pos = handler.lower().find('spinsertdatabaseerror')

                if rollback_pos != -1 and error_call_pos != -1 and rollback_pos < error_call_pos:
                    procedure_name = os.path.basename(sql_file).replace('.sql', '')
                    problematic_procedures.append({
                        'file': sql_file,
                        'procedure': procedure_name,
                        'handler_content': handler.strip()
                    })
                    break

        except Exception as e:
            print(f"Error reading {sql_file}: {e}")

    return problematic_procedures

def find_rollback_before_diagnostics():
    """
    ROLLBACK 후에 GET DIAGNOSTICS를 하는 잘못된 순서의 프로시저들을 찾습니다.
    """

    # DBModel 폴더의 모든 SQL 파일 검색
    sql_files = []
    for root, dirs, files in os.walk("Server/DBModel"):
        for file in files:
            if file.endswith('.sql') and file.startswith('sp'):
                sql_files.append(os.path.join(root, file))

    wrong_order_procedures = []

    for sql_file in sql_files:
        try:
            with open(sql_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # DECLARE EXIT HANDLER 패턴 찾기
            handler_pattern = r'DECLARE\s+EXIT\s+HANDLER\s+FOR\s+SQLEXCEPTION[^;]*?BEGIN(.*?)END;'
            handlers = re.findall(handler_pattern, content, re.DOTALL | re.IGNORECASE)

            for handler in handlers:
                # ROLLBACK과 GET DIAGNOSTICS 순서 확인
                rollback_pos = handler.lower().find('rollback')
                diagnostics_pos = handler.lower().find('get diagnostics')

                if rollback_pos != -1 and diagnostics_pos != -1 and rollback_pos < diagnostics_pos:
                    procedure_name = os.path.basename(sql_file).replace('.sql', '')
                    wrong_order_procedures.append({
                        'file': sql_file,
                        'procedure': procedure_name,
                        'handler_content': handler.strip()
                    })
                    break

        except Exception as e:
            print(f"Error reading {sql_file}: {e}")

    return wrong_order_procedures

def main():
    print("=== ROLLBACK 후 GET DIAGNOSTICS 하는 잘못된 순서의 프로시저 목록 ===\n")

    wrong_order = find_rollback_before_diagnostics()

    if not wrong_order:
        print("ROLLBACK 후 GET DIAGNOSTICS 하는 프로시저를 찾지 못했습니다.")
        print("(이는 좋은 소식입니다 - 모든 프로시저가 올바른 순서를 사용하고 있습니다)")
        return

    print(f"총 {len(wrong_order)}개의 잘못된 순서를 사용하는 프로시저를 발견했습니다:\n")

    # 카테고리별로 분류
    commondb_procs = []
    gamedb_procs = []

    for proc in wrong_order:
        if 'commondb' in proc['file']:
            commondb_procs.append(proc)
        elif 'gamedb' in proc['file']:
            gamedb_procs.append(proc)

    if commondb_procs:
        print("## CommonDB 프로시저:")
        for proc in commondb_procs:
            print(f"- {proc['procedure']} ({proc['file']})")
        print()

    if gamedb_procs:
        print("## GameDB 프로시저:")
        for proc in gamedb_procs:
            print(f"- {proc['procedure']} ({proc['file']})")
        print()

    print("\n=== 심각한 문제 상세 내용 ===")
    print("이 프로시저들은 다음과 같은 심각한 문제가 있습니다:")
    print("1. ROLLBACK을 먼저 실행한 후 GET DIAGNOSTICS를 호출")
    print("2. 원본 에러 정보가 완전히 손실됨")
    print("3. GET DIAGNOSTICS가 ROLLBACK에 대한 정보만 반환함")
    print("4. 실제 에러 원인을 파악할 수 없어 디버깅 불가능")
    print("\n올바른 순서:")
    print("1. GET DIAGNOSTICS (원본 에러 정보 보존)")
    print("2. ROLLBACK (트랜잭션 정리)")
    print("3. CALL spInsertDataBaseError (에러 로깅)")

    print("\n=== 추가 검사: ROLLBACK 후 spInsertDataBaseError 호출 패턴 ===")
    problematic = find_problematic_procedures()
    print(f"ROLLBACK 후 spInsertDataBaseError 호출하는 프로시저: {len(problematic)}개")
    print("(이는 기능적으로는 동작하지만 코드 품질 관점에서 개선 권장)")

if __name__ == "__main__":
    main()
