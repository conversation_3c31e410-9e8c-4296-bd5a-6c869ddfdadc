﻿#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpJoinGuild : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spJoinGuild";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	EErrorCode MakeOutput(NSAdoCommand* command);
	

public:
	EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input : NPGuildMember
	{
		int32_t Wid = 0;
		int64_t GuildId = 0;
		int32_t GearScore = 0;
	} Input;

	struct Output
	{
		uint8_t JoinType = 0;
		char ResultHistory[4001] = {};
	} Output;

	SpJoinGuild() = default;
	SpJoinGuild(const int32_t Wid, const int64_t GuildId, const int32_t GearScore, const NPGuildMember& MemberInfo);
	NPGuildMember ToNPGuildMember() const;
};
