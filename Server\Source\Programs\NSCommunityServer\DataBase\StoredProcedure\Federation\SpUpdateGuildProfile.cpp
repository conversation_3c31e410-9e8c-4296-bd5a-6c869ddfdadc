﻿#include "stdafx.h"
#include "SpUpdateGuildProfile.h"

#include "ADO/NSAdoCommand.h"

SpUpdateGuildProfile::SpUpdateGuildProfile(const int64_t GuildId, const int64_t Cid, const char* Profile) :
	Input{ GuildId, Cid, }
{
	memcpy_s(Input.Profile, sizeof(Input.Profile), Profile, strlen(Profile) + 1);
}

EErrorCode SpUpdateGuildProfile::MakeQuery(NSAdoCommand* command)
{
	// TODO: [Austin] Sanitize the profile to prevent sql injection
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("CID", Input.Cid);
	if (!command->SetItem("Profile", Input.Profile))
		return EErrorCode::DBArgumentError;

	return EErrorCode::None;
}

//EErrorCode SpUpdateGuildProfile::MakeOutput(NSAdoCommand* command)
//{
//	command->GetItem("@InviteUid", Output.InviteUid);
//
//	return EErrorCode::None;
//}

//EErrorCode SpUpdateGuildProfile::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}
