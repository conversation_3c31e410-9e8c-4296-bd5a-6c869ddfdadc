﻿#include "stdafx.h"
#include "SpWithdrawGuildCoffer.h"

#include "ADO/NSAdoCommand.h"

SpWithdrawGuildCoffer::SpWithdrawGuildCoffer(const int64_t GuildId, const int64_t Aid, const int64_t Cid, const int64_t Amount, const uint64_t ResetWithdrawAt, const int64_t EscrowTransactionId) :
	Input{ GuildId, Aid, Cid, Amount, ResetWithdrawAt, EscrowTransactionId }, Data{}, Output{}
{
}

EErrorCode SpWithdrawGuildCoffer::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("AID", Input.Aid);
	command->SetItem("CID", Input.Cid);
	command->SetItem("Amount", Input.Amount);
	command->SetItem("ResetWithdrawAt", Input.ResetWithdrawAt);
	command->SetItem("EscrowTransactionId", Input.EscrowTransactionId);

	return EErrorCode::None;
}

EErrorCode SpWithdrawGuildCoffer::MakeOutput(NSAdoCommand* command)
{
	command->GetItem("Balance", Output.Balance);
	command->GetItem("ResultWithdraw", Output.ResultWithdraw);

	return EErrorCode::None;
}

EErrorCode SpWithdrawGuildCoffer::HandleReturnValue(const int32_t returnValue)
{
	switch (returnValue)
	{
	case EProcReturnValue::GuildMemberInvalidPermission:
		return EErrorCode::GuildMemberInvalidPermission;
	case EProcReturnValue::GuildCofferNotEnoughBalance:
		return EErrorCode::GuildCofferNotEnoughBalance;
	case EProcReturnValue::GuildCofferWithdrawExceedDailyLimit:
		return EErrorCode::GuildCofferWithdrawExceedDailyLimit;
	default:
		return EErrorCode::DBError;
	}
}
