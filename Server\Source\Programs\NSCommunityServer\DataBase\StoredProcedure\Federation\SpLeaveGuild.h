#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpLeaveGuild : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spLeaveGuild";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int64_t GuildId = 0;
		int64_t Cid = 0;
	} Input;

	struct Output
	{
		uint64_t LeaveTimestamp = 0;
		char ResultHistory[4001] = {};
	} Output;

	SpLeaveGuild() = default;
	SpLeaveGuild(const int64_t GuildId, const int64_t Cid);
};
