﻿#include "stdafx.h"
#include "NSStorageUpdater.h"
#include "DataBase/NSStorageModel.h"
#include "DataBase/NSDataBaseManager.h"
#include "Service.h"
#include "Guid/NSGuidManager.h"

NSStorageUpdater::NSStorageUpdater(int64_t aid, int64_t cid, [[maybe_unused]] std::source_location location)
	: m_Wid(Service::GetInstance()->GetWid()), m_Aid(aid), m_Cid(cid)
{
#ifdef _DEBUG
	LOGD << std::format("NSStorageUpdater declared from {}:{}", location.file_name(), location.line());
#endif
}

NSStorageUpdater::~NSStorageUpdater()
{
#ifdef _DEBUG
	assert(m_CommitCalled);
#endif
}

void NSStorageUpdater::MakeEscrowPayload(rapidjson::Writer<rapidjson::StringBuffer>& writer, const NSEscrowData& escrowData)
{
	writer.StartObject();
	{
		writer.Key("SID");
		writer.Int64(escrowData.SenderCid);

		writer.Key("RID");
		writer.Int64(escrowData.ReceiverCid);

		writer.Key("T");
		writer.Int(NPClassCast(escrowData.EscrowType));

		writer.Key("I");
		writer.Int(escrowData.EscrowId);

		writer.Key("Q");
		writer.Int(escrowData.EscrowQuantity);

		writer.Key("UID");
		writer.Int64(escrowData.EscrowUid);
	}
	writer.EndObject();
}

void NSStorageUpdater::AddModelToUpdate(NSStorageModel* componentModel, [[maybe_unused]] std::source_location location)
{
#ifdef _DEBUG
	LOGI << std::format("NSStorageUpdater::AddModelToUpdate() called from {}:{}", location.file_name(), location.line());
#endif

	if (componentModel == nullptr)
	{
		LOGE << std::format("NSStorageUpdater::AddModelToUpdate() has called with empty component.");
		return;
	}

	int32_t typeId = componentModel->GetModelId();
	if (m_ModelIds.insert(typeId).second)
	{
		int64_t newSeq = NSStorageManager::GetInstance()->GetNextSequence(m_Cid);
		if (newSeq == -1)
		{
			LOGE << std::format("NSStorageUpdater::AddModelToUpdate() failed to get a new sequence.");
			DisconnectSession(m_Cid, EErrorCode::FailedToGetNewSequence);
			return;
		}
		m_Models.push_back(std::make_tuple(newSeq, componentModel));
	}
	else
	{

	}
}

void NSStorageUpdater::AddResultFunc(const TYPE_RESULT_MODEL_FUNC& resultFunc)
{
	m_ResultFuncs.push_back(resultFunc);
}

void NSStorageUpdater::Rollback([[maybe_unused]] std::source_location location)
{
#ifdef _DEBUG
	assert(!m_CommitCalled);
	LOGD << std::format("NSStorageUpdater::Rollback() has started from {}:{}", location.file_name(), location.line());
	m_CommitCalled = true;
#endif

	for (auto& [seq, model] : m_Models)
	{
		model->Rollback();
	}
}

void NSStorageUpdater::Commit(const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
	QueryFunc(EStorageUpdateMethod::Normal, resultFunc, location, false, 0);
}

void NSStorageUpdater::CommitForce(const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
	QueryFunc(EStorageUpdateMethod::Normal, resultFunc, location, true, 0);
}

void NSStorageUpdater::CommitWithEscrowDeposit(const int64_t escrowReceiverCid, const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
	m_EscrowReceiverCid = escrowReceiverCid;
	QueryFunc(EStorageUpdateMethod::EscrowDeposit, resultFunc, location, false, 0);
}

void NSStorageUpdater::CommitWithEscrowWithdraw(int64_t escrowTransactionId, const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
	QueryFunc(EStorageUpdateMethod::EscrowWithdraw, resultFunc, location, false, escrowTransactionId);
}

void NSStorageUpdater::QueryFunc(EStorageUpdateMethod method, const TYPE_RESULT_MODEL_FUNC& resultFunc, [[maybe_unused]] std::source_location location, bool isForce, int64_t escrowTransactionId)
{
#ifdef _DEBUG
	assert(!m_CommitCalled);
	LOGD << std::format("NSStorageUpdater::Commit() has started from {}:{}", location.file_name(), location.line());
	m_CommitCalled = true;

	int64_t debugPendingSeq = NSStorageManager::GetInstance()->GetPendingSequence(m_Cid);
	if (debugPendingSeq != 0)
	{
		size_t debugEstimateCount = debugPendingSeq - NSStorageManager::GetInstance()->GetUpdatedSequence(m_Cid);
		if (m_Models.size() != debugEstimateCount)
		{
			LOGW << std::format("NSStorageUpdater::Commit() estimateCount mismatch. PendingCount: {}, ModelCount: {}", debugEstimateCount, m_Models.size());
		}
	}
#endif

	if (method != EStorageUpdateMethod::Normal &&
		method != EStorageUpdateMethod::CustomProcedure &&
		m_Models.size() != 1)
	{
		LOGE << std::format("NSStorageUpdater::Commit() only allows 1 model with escrow");
		return;
	}

	if (resultFunc != nullptr)
	{
		AddResultFunc(resultFunc);
	}

	int64_t newEscrowTransactionId = method == EStorageUpdateMethod::EscrowDeposit
		? NSGuidManager::GetInstance()->GenerateGuid(NSGuidManager::Guid::Escrow)
		: 0;

	size_t currentIdx = 0;
	size_t modelCount = m_Models.size();
	for (auto& [seq, model] : m_Models)
	{
		++currentIdx;
		if (model == nullptr)
		{
			LOGW << std::format("NSStorageUpdater::Commit() found a nullptr model");
			continue;
		}

		auto container = std::make_shared<NSStorageUpdateContainer>();
		container->Wid = m_Wid;
		container->Aid = m_Aid;
		container->Cid = m_Cid;
		container->Seq = seq;
		container->ModelId = model->GetModelId();
		container->Method = method;

		switch (method)
		{
		case EStorageUpdateMethod::Normal:
		{
			container->ProcedureName = model->GetUpsertName();
		}
		break;
		case EStorageUpdateMethod::EscrowDeposit:
		{
			NSStorageEscrowModel* escrowModel = static_cast<NSStorageEscrowModel*>(model);
			if (escrowModel == nullptr)
			{
				LOGE << std::format("NSStorageUpdater::Commit() Model #{} is not an escrow model.", model->GetModelId());
				return;
			}
			container->ProcedureName = escrowModel->GetEscrowDepositName();
			container->EscrowTransactionId = newEscrowTransactionId;
			escrowModel->SerializeEscrow(container->EscrowPayload, m_EscrowReceiverCid);
		}
		break;
		case EStorageUpdateMethod::EscrowWithdraw:
		{
			NSStorageEscrowModel* escrowModel = static_cast<NSStorageEscrowModel*>(model);
			if (escrowModel == nullptr)
			{
				LOGE << std::format("NSStorageUpdater::Commit() Model #{} is not an escrow model.", model->GetModelId());
				return;
			}
			container->ProcedureName = escrowModel->GetEscrowWithdrawName();
			container->EscrowTransactionId = escrowTransactionId;
		}
		break;
		}

		if (container->ProcedureName.empty())
		{
			LOGE << std::format("NSStorageUpdater::Commit() Model #{} has invalid UpsertName.", model->GetModelId());
			continue;
		}

		if (method == EStorageUpdateMethod::Normal && isForce)
		{
			model->Serialize(container->Payloads);
		}
		else
		{
			model->SerializeAffected(container->Payloads);
		}

		if (container->Payloads.empty())
		{
			LOGD << std::format("NSStorageUpdater::Commit() Model #{} has empty payload", model->GetModelId());
			continue;
		}

		if (currentIdx == modelCount)
		{
			container->Callbacks.swap(m_ResultFuncs);
		}

		PostQuery(container);
	}
}

bool NSStorageUpdater::PostQuery(const std::shared_ptr<NSStorageUpdateContainer> container)
{
	if (!NSStorageManager::GetInstance()->IsUpdatePossible(container->Cid))
		return PostQueryDelay(1000, container);

	if (container->ProcedureName.empty())
	{
		LOGE << std::format("NSStorageUpdater::PostQuery() Model #{} has invalid UpsertName.", container->ModelId);
		return false;
	}

#ifdef _DEBUG
	LOGD << std::format("NSStorageUpdater::PostQuery() Model #{} seq {}", container->ModelId, container->Seq);
	LOGD << std::format("NSStorageUpdater::PostQuery() Model #{} has {} payloads.", container->ModelId, container->Payloads.size());
#endif

	auto session = NSSessionManager::GetInstance()->GetSessionByCID(container->Cid);
	NSDataBaseManager::GetInstance()->StorageUpdateQuery(container,
		std::bind(&NSStorageUpdater::UpdateFunc, std::placeholders::_1, std::placeholders::_2),
		std::bind(&NSStorageUpdater::ResultFunc, std::placeholders::_1, std::placeholders::_2),
		session);

	return true;
}

bool NSStorageUpdater::PostQueryDelay(int delayMS, const std::shared_ptr<NSStorageUpdateContainer> container)
{
#ifdef _DEBUG
	LOGD << std::format("NSStorageUpdater::PostQueryDelay() has started. delayMS: {}", delayMS);
#endif

	Post(Promise::ThreadPool(),
		[=]()
		{
			std::this_thread::sleep_for(std::chrono::milliseconds(delayMS));
			PostQuery(container);
		});
	return true;
}

EErrorCode NSStorageUpdater::UpdateFunc(const std::shared_ptr<NSQueryData> queryData, const std::shared_ptr<NSStorageUpdateContainer> container)
{
	const auto procedureHost = EDataBase::Game;
	auto connection = NSDataBaseManager::GetInstance()->GetDBConnection(procedureHost);
	if (connection == nullptr)
	{
		LOGE << std::format("NSStorageUpdater::UpdateFunc() failed to connect database. Model #{} seq {} procedure {}",
			container->ModelId, container->Seq, container->ProcedureName);
		NSStorageManager::GetInstance()->AddFailCount(container->Cid);
		PostQueryDelay(1000, container);
		return EErrorCode::DBConnectionError;
	}

	NSAdoCommand* command = nullptr;
	command = connection->GetCommand(container->ProcedureName.c_str());
	if (command == nullptr)
	{
		LOGE << std::format("NSStorageUpdater::UpdateFunc() procedure not found from database. procedure {}", container->ProcedureName);
		DisconnectSession(container->Cid, EErrorCode::AdoCommandNullptr);
		return EErrorCode::AdoCommandNullptr;
	}

	command->SetItem("WID", container->Wid);
	command->SetItem("AID", container->Aid);
	command->SetItem("CID", container->Cid);
	command->SetItem("SEQ", container->Seq);

	if (container->Method == EStorageUpdateMethod::EscrowDeposit)
	{
		command->SetItem("EscrowTransactionId", container->EscrowTransactionId);
		command->SetItem("EscrowPayload", container->EscrowPayload.c_str());
	}
	else if (container->Method == EStorageUpdateMethod::EscrowWithdraw)
	{
		command->SetItem("EscrowTransactionId", container->EscrowTransactionId);
	}

	size_t payloadCount = container->Payloads.size();
	for (size_t i = 0; i < payloadCount; ++i)
	{
		if (!container->Payloads.at(i).empty() && container->Payloads.at(i) != "[]")
		{
			LOGI << std::format("NSStorageUpdater::UpdateFunc() Payload{}: {}", i, container->Payloads.at(i).c_str());
		}
		command->SetItem(std::format("Payload{}", i).c_str(), container->Payloads.at(i).c_str());
	}

	if (!command->Execute(queryData.get()))
	{
		LOGE << std::format("NSStorageUpdater::UpdateFunc() failed to execute {}. ReturnValue [{}]",
			container->ProcedureName, command->GetReturnValue());
		// retry? error?
		// disconnect test
		DisconnectSession(container->Cid, EErrorCode::DBError);
		return EErrorCode::DBError;
	}

#ifdef _DEBUG
	LOGD << std::format("NSStorageUpdater::UpdateFunc() has executed {} for seq {}.", container->ProcedureName, container->Seq);
#endif

	return ProcessSequence(command->GetReturnValue(), container);
}

EErrorCode NSStorageUpdater::ProcessSequence(int32_t returnValue, const std::shared_ptr<NSStorageUpdateContainer> container)
{
	if (returnValue != 0)
	{
		LOGE << std::format("NSStorageUpdater::ProcessSequence() found error return. {}", returnValue);
		if (returnValue == 49999)
		{
			LOGE << std::format("NSStorageUpdater::ProcessSequence() CID: {}, updatedSeq: {}, requestedSeq: {}",
				container->Cid, NSStorageManager::GetInstance()->GetUpdatedSequence(container->Cid), container->Seq);
		}
		DisconnectSession(container->Cid, EErrorCode::DBError);
		return EErrorCode::DBError;
	}

	auto session = NSSessionManager::GetInstance()->GetSessionByCID(container->Cid);
	if (session == nullptr)
	{
		// relay to another game servers
#ifdef _DEBUG
		LOGD << std::format("NSStorageUpdater::ProcessSequence() session is not exists. relay the event to other processes...");
#endif
	}
	else
	{
		NSStorageManager::GetInstance()->SetUpdatedSequence(container->Cid, container->Seq);
	}

	return EErrorCode::None;
}

EErrorCode NSStorageUpdater::ResultFunc(const std::shared_ptr<NSQueryData> queryData, const std::shared_ptr<NSStorageUpdateContainer> container)
{
	if (queryData->GetErrorCode() != EErrorCode::None)
		return queryData->GetErrorCode();

	if (!container->Callbacks.empty())
	{
		for (auto& callback : container->Callbacks)
		{
			callback(queryData.get(), container.get());
		}
	}

	return EErrorCode::None;
}

void NSStorageUpdater::DisconnectSession(int64_t cid, EErrorCode error)
{
	auto session = NSSessionManager::GetInstance()->GetSessionByCID(cid);
	if (session != nullptr)
	{
		session->SendSystemNtfThenClose(error);
	}
}
