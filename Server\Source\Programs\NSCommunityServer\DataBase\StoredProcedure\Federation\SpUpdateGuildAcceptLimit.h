#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpUpdateGuildAcceptLimit : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spUpdateGuildAcceptLimit";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	//EErrorCode MakeOutput(NSAdoCommand* command);
	//EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int64_t GuildId = 0;
		int64_t Cid = 0;
		int32_t AcceptLevel = 0;
		int32_t AcceptGearScore = 0;
	} Input;

	SpUpdateGuildAcceptLimit() = default;
	SpUpdateGuildAcceptLimit(const int64_t GuildId, const int64_t Cid, const int32_t AcceptLevel, const int32_t AcceptGearScore);
};
