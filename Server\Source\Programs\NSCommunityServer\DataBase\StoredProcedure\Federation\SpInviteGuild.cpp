﻿#include "stdafx.h"
#include "SpInviteGuild.h"

#include "ADO/NSAdoCommand.h"

#include "Data/NSGameConfigTemplate.h"
#include "Data/NSContentCooldownTemplate.h"

SpInviteGuild::SpInviteGuild(const int32_t Wid, const int64_t Cid, const int64_t ReceiverCid, const int64_t GuildId) :
	Input{ Wid, Cid, ReceiverCid, GuildId }
{
}

EErrorCode SpInviteGuild::MakeQuery(NSAdoCommand* command)
{
	static const auto penaltyId = NSGameConfigTemplate::GetInstance()->GetGuildPenaltyCooldownId();
	static const auto penaltySec = NSContentCooldownTemplate::GetInstance()->GetContentCooldownSec(penaltyId);
	static const auto timeLimitId = NSGameConfigTemplate::GetInstance()->GetGuildInviteAnswerTimeLimitId();
	static const auto timeLimitSec = NSContentCooldownTemplate::GetInstance()->GetContentCooldownSec(timeLimitId);

	command->SetItem("WID", Input.Wid);
	command->SetItem("CID", Input.Cid);
	command->SetItem("ReceiverCID", Input.ReceiverCid);
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("PenaltySec", penaltySec);
	command->SetItem("ValidSec", timeLimitSec);

	return EErrorCode::None;
}

//EErrorCode SpInviteGuild::MakeOutput(NSAdoCommand* command)
//{
//	command->GetItem("@InviteUid", Output.InviteUid);
//
//	return EErrorCode::None;
//}

EErrorCode SpInviteGuild::HandleReturnValue(const int32_t returnValue)
{
	switch (returnValue)
	{
	case EProcReturnValue::GuildInviteDuplicated:
		return EErrorCode::GuildInviteDuplicated;
	case EProcReturnValue::GuildInviteTargetNotFinishPenalty:
		return EErrorCode::GuildInviteTargetNotFinishPenalty;
	case EProcReturnValue::GuildMemberAlreadyExist:
		return EErrorCode::GuildMemberAlreadyExist;
	default:
		return EErrorCode::DBError;
	}
}
