<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Label="PropertySheets" />
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalIncludeDirectories>$(SolutionDir)Libraries\output\include\;$(SolutionDir)Libraries\vcpkg\installed\$(Platform)-windows\include\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <AdditionalLibraryDirectories>$(SolutionDir)Libraries\output\lib\$(Configuration);$(SolutionDir)Libraries\vcpkg\installed\$(Platform)-windows\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>Rpcrt4.lib;wldap32.lib;crypt32.lib;tinyxml2.lib;cpp_redis.lib;tacopie.lib;sentry.lib;spdlog.lib;libcurl.lib;hiredis.lib;hiredis_ssl.lib;libcrypto.lib;libssl.lib;redis++_static.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup />
</Project>