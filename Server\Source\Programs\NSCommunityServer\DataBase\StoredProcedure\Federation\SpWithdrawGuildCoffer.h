﻿#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpWithdrawGuildCoffer : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spWithdrawGuildCoffer";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);
	EErrorCode MakeOutput(NSAdoCommand* command);


public:
	EErrorCode HandleReturnValue(const int32_t returnValue);

public:
	struct Input
	{
		int64_t GuildId = 0;
		int64_t Aid = 0;
		int64_t Cid = 0;
		int64_t Amount = 0;
		uint64_t ResetWithdrawAt = 0;
		int64_t EscrowTransactionId = 0;
	} Input;

	struct Data
	{
		int32_t CallbackId = 0;
	} Data;

	struct Output
	{
		TYPE_QUANTITY Balance = 0;
		TYPE_QUANTITY ResultWithdraw = 0;
	} Output;

	SpWithdrawGuildCoffer() = default;
	SpWithdrawGuildCoffer(const int64_t GuildId, const int64_t Aid, const int64_t Cid, const int64_t Amount, const uint64_t ResetWithdrawAt, const int64_t EscrowTransactionId);
};
