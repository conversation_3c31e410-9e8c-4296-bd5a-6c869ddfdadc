# ROLLBACK 후 spInsertDataBaseError 호출 문제가 있는 프로시저 목록

## 문제 요약
총 **110개**의 저장 프로시저에서 다음과 같은 문제가 발견되었습니다:

```sql
DECLARE EXIT HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
BEGIN
    GET DIAGNOSTICS CONDITION 1
        errorMessage = MESSAGE_TEXT,
        errorNumber = MYSQL_ERRNO;        
    
    ROLLBACK;  -- 문제: 트랜잭션 롤백이 먼저 실행됨
    CALL spInsertDataBaseError(errorProcedure, errorNumber, errorSeverity, errorLine, errorMessage);  -- 실패 가능성
    SET return_from_handler := TRUE;
    SET ReturnCode = 50001;
END;
```

## 문제점
1. **ROLLBACK을 먼저 실행**한 후 `spInsertDataBaseError`를 호출
2. **ROLLBACK으로 인해 트랜잭션이 종료**되어 `spInsertDataBaseError` 호출이 실패할 수 있음
3. **에러 로깅이 제대로 되지 않을 가능성**

## CommonDB 프로시저 (1개)
- [spInsertReport](Server/DBModel/commondb/spInsertReport.sql)

## GameDB 프로시저 (109개)

### 친구 관리 (Friend Management)
- [spAcceptFriend](Server/DBModel/gamedb/spAcceptFriend.sql)
- [spCancelInviteFriend](Server/DBModel/gamedb/spCancelInviteFriend.sql)
- [spDeclineFriend](Server/DBModel/gamedb/spDeclineFriend.sql)
- [spDeleteFriend](Server/DBModel/gamedb/spDeleteFriend.sql)
- [spInviteFriend](Server/DBModel/gamedb/spInviteFriend.sql)

### 길드 관리 (Guild Management)
- [spAcceptGuild](Server/DBModel/gamedb/spAcceptGuild.sql)
- [spApproveJoinGuild](Server/DBModel/gamedb/spApproveJoinGuild.sql)
- [spCancelJoinGuild](Server/DBModel/gamedb/spCancelJoinGuild.sql)
- [spCreateGuild](Server/DBModel/gamedb/spCreateGuild.sql)
- [spDeclineGuild](Server/DBModel/gamedb/spDeclineGuild.sql)
- [spDismissGuild](Server/DBModel/gamedb/spDismissGuild.sql)
- [spInviteGuild](Server/DBModel/gamedb/spInviteGuild.sql)
- [spJoinGuild](Server/DBModel/gamedb/spJoinGuild.sql)
- [spKickGuildMember](Server/DBModel/gamedb/spKickGuildMember.sql)
- [spLeaveGuild](Server/DBModel/gamedb/spLeaveGuild.sql)
- [spRejectJoinGuild](Server/DBModel/gamedb/spRejectJoinGuild.sql)
- [spTransferGuildMaster](Server/DBModel/gamedb/spTransferGuildMaster.sql)

### 길드 금고 (Guild Coffer)
- [spDepositGuildCoffer](Server/DBModel/gamedb/spDepositGuildCoffer.sql)
- [spGuildCofferResetWithdraw](Server/DBModel/gamedb/spGuildCofferResetWithdraw.sql)
- [spWithdrawGuildCoffer](Server/DBModel/gamedb/spWithdrawGuildCoffer.sql)

### 길드 설정 (Guild Settings)
- [spUpdateGuildAcceptLimit](Server/DBModel/gamedb/spUpdateGuildAcceptLimit.sql)
- [spUpdateGuildAcceptType](Server/DBModel/gamedb/spUpdateGuildAcceptType.sql)
- [spUpdateGuildCofferWithdrawLimit](Server/DBModel/gamedb/spUpdateGuildCofferWithdrawLimit.sql)
- [spUpdateGuildEmblem](Server/DBModel/gamedb/spUpdateGuildEmblem.sql)
- [spUpdateGuildMemberComment](Server/DBModel/gamedb/spUpdateGuildMemberComment.sql)
- [spUpdateGuildMemberGrade](Server/DBModel/gamedb/spUpdateGuildMemberGrade.sql)
- [spUpdateGuildNotice](Server/DBModel/gamedb/spUpdateGuildNotice.sql)
- [spUpdateGuildProfile](Server/DBModel/gamedb/spUpdateGuildProfile.sql)
- [spUpdateGuildSiegeStarTime](Server/DBModel/gamedb/spUpdateGuildSiegeStarTime.sql)

### 거래/마켓 (Deal/Market)
- [spBuyDeal](Server/DBModel/gamedb/spBuyDeal.sql)
- [spBuyDealPartial](Server/DBModel/gamedb/spBuyDealPartial.sql)
- [spCancelDeal](Server/DBModel/gamedb/spCancelDeal.sql)
- [spCancelDealEscrow](Server/DBModel/gamedb/spCancelDealEscrow.sql)
- [spCheckUserDeal](Server/DBModel/gamedb/spCheckUserDeal.sql)
- [spCleanUpDeal](Server/DBModel/gamedb/spCleanUpDeal.sql)
- [spCleanUpDealEscrow](Server/DBModel/gamedb/spCleanUpDealEscrow.sql)
- [spDeductDealItemQuantity](Server/DBModel/gamedb/spDeductDealItemQuantity.sql)
- [spPayDeal](Server/DBModel/gamedb/spPayDeal.sql)
- [spRegisterDeal](Server/DBModel/gamedb/spRegisterDeal.sql)
- [spRegisterDealEscrow](Server/DBModel/gamedb/spRegisterDealEscrow.sql)
- [spReserveDeal](Server/DBModel/gamedb/spReserveDeal.sql)
- [spReserveDealNew](Server/DBModel/gamedb/spReserveDealNew.sql)
- [spRevertDeal](Server/DBModel/gamedb/spRevertDeal.sql)
- [spSettleDeal](Server/DBModel/gamedb/spSettleDeal.sql)
- [spSettleDealEscrow](Server/DBModel/gamedb/spSettleDealEscrow.sql)
- [spSoldDeal](Server/DBModel/gamedb/spSoldDeal.sql)
- [spTransferDealItemOwner](Server/DBModel/gamedb/spTransferDealItemOwner.sql)
- [spUpdateUserDeal](Server/DBModel/gamedb/spUpdateUserDeal.sql)

### 마켓 위시리스트 (Market Wishlist)
- [spAddMarketWishlist](Server/DBModel/gamedb/spAddMarketWishlist.sql)
- [spClearMarketAutoRegist](Server/DBModel/gamedb/spClearMarketAutoRegist.sql)
- [spRemoveMarketMultiWishlist](Server/DBModel/gamedb/spRemoveMarketMultiWishlist.sql)
- [spRemoveMarketWishlist](Server/DBModel/gamedb/spRemoveMarketWishlist.sql)

### 에스크로 (Escrow)
- [spEscrowDepositCharacterGoods](Server/DBModel/gamedb/spEscrowDepositCharacterGoods.sql)
- [spEscrowDepositCharacterItem](Server/DBModel/gamedb/spEscrowDepositCharacterItem.sql)
- [spEscrowReclaimCharacterGoods](Server/DBModel/gamedb/spEscrowReclaimCharacterGoods.sql)
- [spEscrowReclaimCharacterItem](Server/DBModel/gamedb/spEscrowReclaimCharacterItem.sql)
- [spEscrowWithdrawCharacterGoods](Server/DBModel/gamedb/spEscrowWithdrawCharacterGoods.sql)
- [spEscrowWithdrawCharacterItem](Server/DBModel/gamedb/spEscrowWithdrawCharacterItem.sql)

### 캐릭터 관리 (Character Management)
- [spBlockPlayer](Server/DBModel/gamedb/spBlockPlayer.sql)
- [spCancelDeleteCharacter](Server/DBModel/gamedb/spCancelDeleteCharacter.sql)
- [spCreateCharacter](Server/DBModel/gamedb/spCreateCharacter.sql)
- [spLoginAccount](Server/DBModel/gamedb/spLoginAccount.sql)
- [spUnBlockPlayer](Server/DBModel/gamedb/spUnBlockPlayer.sql)
- [spUpdateCharacter](Server/DBModel/gamedb/spUpdateCharacter.sql)
- [spUpdatePvPFlag](Server/DBModel/gamedb/spUpdatePvPFlag.sql)

### 캐릭터 데이터 업데이트 (Character Data Updates)
- [spUpsertAbility](Server/DBModel/gamedb/spUpsertAbility.sql)
- [spUpsertCharacterData](Server/DBModel/gamedb/spUpsertCharacterData.sql)
- [spUpsertCharacterDungeonRecord](Server/DBModel/gamedb/spUpsertCharacterDungeonRecord.sql)
- [spUpsertCharacterGoods](Server/DBModel/gamedb/spUpsertCharacterGoods.sql)
- [spUpsertCharacterItem](Server/DBModel/gamedb/spUpsertCharacterItem.sql)
- [spUpsertCharacterMatrixContents](Server/DBModel/gamedb/spUpsertCharacterMatrixContents.sql)
- [spUpsertCharacterMonsterSoul](Server/DBModel/gamedb/spUpsertCharacterMonsterSoul.sql)
- [spUpsertCharacterPoints](Server/DBModel/gamedb/spUpsertCharacterPoints.sql)
- [spUpsertCharacterStable](Server/DBModel/gamedb/spUpsertCharacterStable.sql)
- [spUpsertCharacterUniquePerk](Server/DBModel/gamedb/spUpsertCharacterUniquePerk.sql)

### 크로노텍터 (Chronotector)
- [spUpsertChronotectorAction](Server/DBModel/gamedb/spUpsertChronotectorAction.sql)
- [spUpsertChronotectorNode](Server/DBModel/gamedb/spUpsertChronotectorNode.sql)
- [spUpsertChronotectorUpgrade](Server/DBModel/gamedb/spUpsertChronotectorUpgrade.sql)

### 스킬/능력 (Skills/Abilities)
- [spActiveSlot_WeaponMastery](Server/DBModel/gamedb/spActiveSlot_WeaponMastery.sql)
- [spUpdatePassiveCooldown](Server/DBModel/gamedb/spUpdatePassiveCooldown.sql)
- [spUpdateSkillCooldown](Server/DBModel/gamedb/spUpdateSkillCooldown.sql)

### 업적/퀘스트 (Achievement/Quest)
- [spUpdateAchievement](Server/DBModel/gamedb/spUpdateAchievement.sql)
- [spUpdateQuest](Server/DBModel/gamedb/spUpdateQuest.sql)

### 코스튬/감정표현 (Costume/Emotion)
- [spResetEmotionInfo](Server/DBModel/gamedb/spResetEmotionInfo.sql)
- [spUpdateCostume](Server/DBModel/gamedb/spUpdateCostume.sql)
- [spUpdateEmotionConfig](Server/DBModel/gamedb/spUpdateEmotionConfig.sql)
- [spUpdateEmotionSlot](Server/DBModel/gamedb/spUpdateEmotionSlot.sql)

### 던전 (Dungeon)
- [spUpdateDungeon](Server/DBModel/gamedb/spUpdateDungeon.sql)

### 기타 업데이트 (Misc Updates)
- [spUpdateRecentPartyPlayer](Server/DBModel/gamedb/spUpdateRecentPartyPlayer.sql)
- [spUpdateStatusEffects](Server/DBModel/gamedb/spUpdateStatusEffects.sql)
- [spUpsertContentUnlock](Server/DBModel/gamedb/spUpsertContentUnlock.sql)
- [spUpsertCoolTime](Server/DBModel/gamedb/spUpsertCoolTime.sql)
- [spUpsertDocumentProp](Server/DBModel/gamedb/spUpsertDocumentProp.sql)
- [spUpsertInitContents](Server/DBModel/gamedb/spUpsertInitContents.sql)
- [spUpsertInventory](Server/DBModel/gamedb/spUpsertInventory.sql)
- [spUpsertItemAccountStorage](Server/DBModel/gamedb/spUpsertItemAccountStorage.sql)
- [spUpsertItemCraft](Server/DBModel/gamedb/spUpsertItemCraft.sql)
- [spUpsertReturnPoint](Server/DBModel/gamedb/spUpsertReturnPoint.sql)
- [spUpsertSaveJsonFormatData](Server/DBModel/gamedb/spUpsertSaveJsonFormatData.sql)
- [spUpsertSettings](Server/DBModel/gamedb/spUpsertSettings.sql)
- [spUpsertStoreUsage](Server/DBModel/gamedb/spUpsertStoreUsage.sql)
- [spUpsertUnlockContents](Server/DBModel/gamedb/spUpsertUnlockContents.sql)

### GM 명령어 (GM Commands)
- [spGmCmdCreateDummyGuild](Server/DBModel/gamedb/spGmCmdCreateDummyGuild.sql)
- [spGmCmdResetGuildSiegeStartTimeCoolDown](Server/DBModel/gamedb/spGmCmdResetGuildSiegeStartTimeCoolDown.sql)
- [spGmCmdSetCharacterGuildJoinTime](Server/DBModel/gamedb/spGmCmdSetCharacterGuildJoinTime.sql)
- [spGmCmdSetCharacterLogoutTime](Server/DBModel/gamedb/spGmCmdSetCharacterLogoutTime.sql)
- [spGmCmdSetGuildCofferBalance](Server/DBModel/gamedb/spGmCmdSetGuildCofferBalance.sql)
- [spGMCommandCreateItem](Server/DBModel/gamedb/spGMCommandCreateItem.sql)

### 기타 (Miscellaneous)
- [spInitRuby](Server/DBModel/gamedb/spInitRuby.sql)

## 권장 해결책

### 1. 에러 로깅을 ROLLBACK 이전으로 이동
```sql
DECLARE EXIT HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
BEGIN
    GET DIAGNOSTICS CONDITION 1
        errorMessage = MESSAGE_TEXT,
        errorNumber = MYSQL_ERRNO;        
    
    -- 먼저 에러 로깅
    CALL spInsertDataBaseError(errorProcedure, errorNumber, errorSeverity, errorLine, errorMessage);
    
    -- 그 다음 롤백
    ROLLBACK;
    SET return_from_handler := TRUE;
    SET ReturnCode = 50001;
END;
```

### 2. 별도 트랜잭션에서 에러 로깅
```sql
DECLARE EXIT HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
BEGIN
    GET DIAGNOSTICS CONDITION 1
        errorMessage = MESSAGE_TEXT,
        errorNumber = MYSQL_ERRNO;        
    
    ROLLBACK;
    
    -- 새로운 트랜잭션에서 에러 로깅
    START TRANSACTION;
    CALL spInsertDataBaseError(errorProcedure, errorNumber, errorSeverity, errorLine, errorMessage);
    COMMIT;
    
    SET return_from_handler := TRUE;
    SET ReturnCode = 50001;
END;
```

## 영향도
이 문제는 **MMO 게임의 핵심 기능들**에 영향을 미칩니다:
- 캐릭터 생성/관리
- 길드 시스템
- 아이템 거래/마켓
- 친구 시스템
- 에스크로 시스템

에러 로깅이 실패하면 **디버깅과 문제 추적이 어려워질** 수 있습니다.
