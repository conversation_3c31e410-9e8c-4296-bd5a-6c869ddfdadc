﻿#include "stdafx.h"
#include "SpJoinGuild.h"

#include "ADO/NSAdoCommand.h"

#include "Data/NSGameConfigTemplate.h"
#include "Data/NSContentCooldownTemplate.h"

SpJoinGuild::SpJoinGuild(const int32_t Wid, const int64_t GuildId, const int32_t GearScore, const NPGuildMember& MemberInfo) :
	Input{ {}, Wid, GuildId, GearScore }
{
	Input.IsOnline = MemberInfo.IsOnline;
	Input.Aid = MemberInfo.Aid;
	Input.Cid = MemberInfo.Cid;
	Input.Level = MemberInfo.Level;
	Input.LastActivity = MemberInfo.LastActivity;
	Input.ClassType = MemberInfo.ClassType;
	Input.ClassAwakenType = MemberInfo.ClassAwakenType;
	Input.Grade = MemberInfo.Grade;
	strcpy_s(Input.Name, sizeof(Input.Name), MemberInfo.Name);
	strcpy_s(Input.Comment, sizeof(Input.Comment), MemberInfo.Comment);
	Input.PlatformType = MemberInfo.PlatformType;
	strcpy_s(Input.PlatformID, sizeof(Input.PlatformID), MemberInfo.PlatformID);
	Input.IdentityProviderType = MemberInfo.IdentityProviderType;
	strcpy_s(Input.IdentityProviderID, sizeof(Input.IdentityProviderID), MemberInfo.IdentityProviderID);
}

EErrorCode SpJoinGuild::MakeQuery(NSAdoCommand* command)
{
	static const auto penaltyId = NSGameConfigTemplate::GetInstance()->GetGuildPenaltyCooldownId();
	static const auto penaltySec = NSContentCooldownTemplate::GetInstance()->GetContentCooldownSec(penaltyId);

	command->SetItem("WID", Input.Wid);
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("CID", Input.Cid);
	if (!command->SetItem("Name", Input.Name))
		return EErrorCode::DBArgumentError;
	command->SetItem("Level", Input.Level);
	command->SetItem("GearScore", Input.GearScore);
	command->SetItem("ClassType", static_cast<uint8_t>(Input.ClassType));
	if (!command->SetItem("Comment", Input.Comment))
		return EErrorCode::DBArgumentError;
	command->SetItem("PenaltySec", penaltySec);

	return EErrorCode::None;
}

EErrorCode SpJoinGuild::MakeOutput(NSAdoCommand* command)
{
	command->GetItem("JoinType", Output.JoinType);
	command->GetItem("ResultHistory", Output.ResultHistory, 4000);

	return EErrorCode::None;
}

EErrorCode SpJoinGuild::HandleReturnValue(const int32_t returnValue)
{
	switch (returnValue)
	{
	case EProcReturnValue::GuildMemberAlreadyExist:
		return EErrorCode::GuildMemberAlreadyExist;
	case EProcReturnValue::GuildJoinNotFinishPenalty:
		return EErrorCode::GuildJoinNotFinishPenalty;
	case EProcReturnValue::GuildNotFound:
		return EErrorCode::GuildNotFound;
	case EProcReturnValue::GuildMemberMaxOut:
		return EErrorCode::GuildMemberMaxOut;
	case EProcReturnValue::GuildAcceptTypeConditionError:
		return EErrorCode::GuildAcceptTypeConditionError;
	case EProcReturnValue::GuildAcceptLevelConditionError:
		return EErrorCode::GuildAcceptLevelConditionError;
	case EProcReturnValue::GuildAcceptGearScoreConditionError:
		return EErrorCode::GuildAcceptGearScoreConditionError;
	default:
		return EErrorCode::DBError;
	}
}

NPGuildMember SpJoinGuild::ToNPGuildMember() const
{
	NPGuildMember guildMember{};

	guildMember.IsOnline = Input.IsOnline;
	guildMember.Aid = Input.Aid;
	guildMember.Cid = Input.Cid;
	guildMember.Level = Input.Level;
	guildMember.LastActivity = Input.LastActivity;
	guildMember.ClassType = Input.ClassType;
	guildMember.Grade = Input.Grade;
	guildMember.ClassAwakenType = Input.ClassAwakenType;
	strcpy_s(guildMember.Name, sizeof(guildMember.Name), Input.Name);
	strcpy_s(guildMember.Comment, sizeof(guildMember.Comment), Input.Comment);
	guildMember.PlatformType = Input.PlatformType;
	strcpy_s(guildMember.PlatformID, sizeof(guildMember.PlatformID), Input.PlatformID);
	guildMember.IdentityProviderType = Input.IdentityProviderType;
	strcpy_s(guildMember.IdentityProviderID, sizeof(guildMember.IdentityProviderID), Input.IdentityProviderID);

	return guildMember;
}