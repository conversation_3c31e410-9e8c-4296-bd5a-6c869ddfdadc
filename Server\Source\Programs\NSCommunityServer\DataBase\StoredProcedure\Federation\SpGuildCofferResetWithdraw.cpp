﻿#include "stdafx.h"
#include "SpGuildCofferResetWithdraw.h"

#include "ADO/NSAdoCommand.h"

SpGuildCofferResetWithdraw::SpGuildCofferResetWithdraw(const int64_t GuildId, const uint64_t NextResetTimestamp) :
	Input{ GuildId, NextResetTimestamp }
{
}

EErrorCode SpGuildCofferResetWithdraw::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("GuildID", Input.GuildId);
	command->SetItem("NextResetTimestamp", Input.NextResetTimestamp);

	return EErrorCode::None;
}

//EErrorCode SpGuildCofferResetWithdraw::MakeOutput(NSAdoCommand* command)
//{
//	command->GetItem("@IsMember", Output.IsMember);
//
//	return EErrorCode::None;
//}

//EErrorCode SpGuildCofferResetWithdraw::HandleReturnValue(const int32_t returnValue)
//{
//	switch (returnValue)
//	{
//	case 101:
//		return EErrorCode::FriendRequestDuplicated;
//	case 102:
//		return EErrorCode::FriendRequestFullByReceiver;
//	default:
//		return EErrorCode::DBError;
//	}
//}
